# -*- coding: utf-8 -*-
"""
3D小人动画显示模块
用于瞳孔生物反馈训练中的3D小人可视化

作者: AI Assistant
日期: 2025-07-22
"""

import os
import sys
import numpy as np
import math
import csv
from psychopy import visual, core
from config import config

class Walk3DRenderer:
    """3D小人动画渲染器"""
    
    def __init__(self, win):
        """
        初始化3D渲染器

        参数:
        - win: PsychoPy窗口对象
        """
        self.win = win
        self.animation_data = {}  # 存储所有动画数据
        self.current_frame = 0
        self.last_frame_time = 0
        self.dots = []  # 存储圆点对象
        self.dilation_data = {}  # 存储情绪编号对应的缩放和位置数据

        # 加载所有动画数据
        self.load_all_animations()

        # 加载缩放和位置调整数据
        self.load_dilation_data()

        # 创建圆点对象
        self.create_dots()
    
    def load_all_animations(self):
        """加载所有情绪等级的动画数据"""
        # 情绪等级从-6到6
        emotion_levels = list(range(-6, 7))
        
        for level in emotion_levels:
            file_path = os.path.join(config.walk_3d_folder, f"{level}.txt")
            if os.path.exists(file_path):
                self.animation_data[level] = self.load_animation_file(file_path)
                print(f"加载动画文件: {file_path}")
            else:
                print(f"警告: 动画文件不存在: {file_path}")
    
    def load_animation_file(self, file_path):
        """
        加载单个动画文件
        
        参数:
        - file_path: 动画文件路径
        
        返回:
        - frames: 动画帧数据列表，每帧包含15个身体部位的3D坐标
        """
        frames = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 解析每一行数据 - 数据用逗号分隔
                    parts = line.replace(',', ' ').split()
                    if len(parts) < 46:  # 1个帧号 + 15个部位 * 3个坐标 = 46
                        continue
                    
                    frame_num = int(parts[0])
                    
                    # 提取15个身体部位的3D坐标
                    body_parts = []
                    for i in range(15):
                        x = float(parts[1 + i * 3])
                        y = float(parts[1 + i * 3 + 1])
                        z = float(parts[1 + i * 3 + 2])
                        body_parts.append([x, y, z])
                    
                    frames.append(body_parts)
        
        except Exception as e:
            print(f"加载动画文件错误: {file_path}, 错误: {e}")
            return []
        
        return frames

    def load_dilation_data(self):
        """加载情绪编号对应的缩放比例和位置变化数据"""
        dilation_file = os.path.join(config.walk_3d_folder, "dilation.csv")

        try:
            with open(dilation_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    emotion_id = int(row['情绪编号'])
                    scale_ratio = float(row['缩放比例'])

                    # 解析位置变化
                    position_change = row['位置变化'].strip()
                    y_offset = 0
                    if position_change != '0':
                        if position_change.startswith('y+'):
                            y_offset = int(position_change[2:])
                        elif position_change.startswith('y-'):
                            y_offset = -int(position_change[2:])

                    self.dilation_data[emotion_id] = {
                        'scale': scale_ratio,
                        'y_offset': y_offset
                    }

            print(f"加载缩放数据文件: {dilation_file}")
            print(f"加载了 {len(self.dilation_data)} 个情绪等级的缩放数据")

        except Exception as e:
            print(f"加载缩放数据文件错误: {dilation_file}, 错误: {e}")
            # 如果加载失败，使用默认值
            for emotion_id in range(-6, 7):
                self.dilation_data[emotion_id] = {'scale': 1.0, 'y_offset': 0}

    def project_3d_to_2d(self, x, y, z, emotion_level=0):
        """
        将3D坐标投影到2D屏幕坐标，并应用情绪等级对应的缩放和位置调整

        参数:
        - x, y, z: 3D坐标
        - emotion_level: 情绪等级，用于应用对应的缩放和位置调整

        返回:
        - screen_x, screen_y: 2D屏幕坐标
        """
        # 应用观看角度旋转
        angle_rad = math.radians(-config.viewing_angle)

        # 绕Y轴旋转
        rotated_x = x * math.cos(angle_rad) + z * math.sin(angle_rad)
        rotated_z = -x * math.sin(angle_rad) + z * math.cos(angle_rad)

        # 透视投影
        # 假设相机在(0, 0, projection_distance)位置看向原点
        # if rotated_z + config.projection_distance != 0:
        #     screen_x = (rotated_x * config.projection_distance) / (rotated_z + config.projection_distance)
        #     screen_y = (y * config.projection_distance) / (rotated_z + config.projection_distance)
        # else:
            # screen_x = rotated_x
            # screen_y = y

        #正交
        screen_x = rotated_x
        screen_y = y

        # 获取情绪等级对应的缩放比例和位置偏移
        if emotion_level in self.dilation_data:
            scale_ratio = self.dilation_data[emotion_level]['scale']
            y_offset = self.dilation_data[emotion_level]['y_offset']
        else:
            scale_ratio = 1.0
            y_offset = 0

        # 应用缩放比例
        screen_x *= scale_ratio
        screen_y *= scale_ratio

        # 缩放到显示区域
        scale_factor = config.display_area_size / 2000  # 原始坐标范围是-1000到+1000
        screen_x *= scale_factor
        screen_y *= scale_factor

        # 添加显示中心偏移
        screen_x += config.display_center_x
        screen_y += config.display_center_y

        # 应用y坐标偏移（在屏幕坐标系中）
        screen_y += y_offset

        #上下翻转
        screen_y = 2 * config.display_center_y - screen_y

        return screen_x, screen_y
    
    def create_dots(self):
        """创建15个圆点对象"""
        self.dots = []
        for i in range(15):
            dot = visual.Circle(
                win=self.win,
                pos=(0, 0),
                radius=config.dot_radius,
                fillColor=config.dot_fill_color,
                lineColor=config.dot_line_color,
                lineWidth=config.dot_line_width
            )
            self.dots.append(dot)
    
    def pupil_to_emotion_level(self, current_pupil_mm, baseline_pupil_mm):
        """
        将瞳孔大小映射到情绪等级
        
        参数:
        - current_pupil_mm: 当前瞳孔大小 (mm)
        - baseline_pupil_mm: 基线瞳孔大小 (mm)
        
        返回:
        - emotion_level: 情绪等级 (-6到6)
        """
        if baseline_pupil_mm is None or baseline_pupil_mm <= 0 or current_pupil_mm is None:
            return 0  # 默认中性情绪

        # 计算瞳孔差值
        pupil_diff = current_pupil_mm - baseline_pupil_mm
        
        # 应用映射公式: pupil = k*(实时瞳孔-基线)
        pupil_value = config.pupil_emotion_scale * pupil_diff
        
        # 映射到情绪等级
        # 瞳孔越大(正值)应该越开心(负的情绪等级)
        # 瞳孔越小(负值)应该越伤心(正的情绪等级)
        emotion_level = -int(pupil_value / config.emotion_step_size)
        
        # 限制在-6到6范围内
        emotion_level = max(-6, min(6, emotion_level))
        
        return emotion_level
    
    def get_frame_data(self, emotion_level, frame_index):
        """
        获取指定情绪等级和帧索引的动画数据
        
        参数:
        - emotion_level: 情绪等级 (-6到6)
        - frame_index: 帧索引 (0到29)
        
        返回:
        - frame_data: 该帧的15个身体部位3D坐标，如果没有数据返回None
        """
        if emotion_level not in self.animation_data:
            return None
        
        frames = self.animation_data[emotion_level]
        if not frames or frame_index >= len(frames):
            return None
        
        return frames[frame_index]
    
    def update_dots_positions(self, emotion_level, frame_index):
        """
        更新圆点位置

        参数:
        - emotion_level: 情绪等级
        - frame_index: 帧索引
        """
        frame_data = self.get_frame_data(emotion_level, frame_index)
        if frame_data is None:
            return False

        # 更新每个圆点的位置，应用情绪等级对应的缩放和位置调整
        for i, (x, y, z) in enumerate(frame_data):
            if i < len(self.dots):
                screen_x, screen_y = self.project_3d_to_2d(x, y, z, emotion_level)
                self.dots[i].pos = (screen_x, screen_y)

        return True
    
    def set_dots_color(self, fill_color, line_color=None):
        """
        设置所有圆点的颜色
        
        参数:
        - fill_color: 填充颜色
        - line_color: 边框颜色，如果为None则使用默认颜色
        """
        if line_color is None:
            line_color = config.dot_line_color
        
        for dot in self.dots:
            dot.fillColor = fill_color
            dot.lineColor = line_color
    
    def draw_current_frame(self):
        """绘制当前帧的所有圆点"""
        for dot in self.dots:
            dot.draw()
    
    def get_total_frames(self, emotion_level):
        """
        获取指定情绪等级的总帧数
        
        参数:
        - emotion_level: 情绪等级
        
        返回:
        - 总帧数，如果没有数据返回0
        """
        if emotion_level not in self.animation_data:
            return 0
        return len(self.animation_data[emotion_level])

# 显示函数接口，供主训练程序调用
def show_3d_baseline(win, renderer, baseline_pupil_mm):
    """
    基线阶段的3D小人显示 - 显示情绪0的动态动画

    参数:
    - win: PsychoPy窗口
    - renderer: 3D渲染器
    - baseline_pupil_mm: 基线瞳孔大小
    """
    # 基线阶段显示中性情绪(0级)的动态动画
    emotion_level = 0

    # 计算当前应该显示的帧（动态动画）
    current_time = core.getTime()
    if not hasattr(renderer, 'baseline_animation_start_time'):
        renderer.baseline_animation_start_time = current_time

    elapsed_time = current_time - renderer.baseline_animation_start_time
    total_frames = renderer.get_total_frames(emotion_level)

    if total_frames > 0:
        # 循环播放动画
        frame_index = int((elapsed_time * config.animation_fps) % total_frames)
    else:
        frame_index = 0

    # 设置默认颜色
    renderer.set_dots_color(config.dot_fill_color, config.dot_line_color)

    # 更新位置并绘制
    if renderer.update_dots_positions(emotion_level, frame_index):
        renderer.draw_current_frame()

    return True

def show_3d_modulation(win, renderer, current_pupil_mm, baseline_pupil_mm):
    """
    自主调适阶段的3D小人显示
    
    参数:
    - win: PsychoPy窗口
    - renderer: 3D渲染器
    - current_pupil_mm: 当前瞳孔大小
    - baseline_pupil_mm: 基线瞳孔大小
    
    返回:
    - emotion_level: 当前情绪等级
    """
    # 计算当前情绪等级
    emotion_level = renderer.pupil_to_emotion_level(current_pupil_mm, baseline_pupil_mm)
    
    # 计算当前应该显示的帧
    current_time = core.getTime()
    if not hasattr(renderer, 'animation_start_time'):
        renderer.animation_start_time = current_time
    
    elapsed_time = current_time - renderer.animation_start_time
    total_frames = renderer.get_total_frames(emotion_level)
    
    if total_frames > 0:
        # 循环播放动画
        frame_index = int((elapsed_time * config.animation_fps) % total_frames)
    else:
        frame_index = 0
    
    # 设置默认颜色
    renderer.set_dots_color(config.dot_fill_color, config.dot_line_color)
    
    # 更新位置并绘制
    if renderer.update_dots_positions(emotion_level, frame_index):
        renderer.draw_current_frame()
    
    return emotion_level

def show_3d_feedback(win, renderer, best_effect_pupil_mm, baseline_pupil_mm, success):
    """
    反馈阶段的3D小人显示 - 显示动态的最佳调节效果情绪等级动画

    参数:
    - win: PsychoPy窗口
    - renderer: 3D渲染器
    - best_effect_pupil_mm: 最佳效果瞳孔大小
    - baseline_pupil_mm: 基线瞳孔大小
    - success: 是否成功
    """
    # 计算最佳效果对应的情绪等级
    if best_effect_pupil_mm is not None and baseline_pupil_mm is not None:
        emotion_level = renderer.pupil_to_emotion_level(best_effect_pupil_mm, baseline_pupil_mm)
    else:
        emotion_level = 0  # 默认中性情绪

    # 计算当前应该显示的帧（动态动画）
    current_time = core.getTime()
    if not hasattr(renderer, 'feedback_animation_start_time'):
        renderer.feedback_animation_start_time = current_time

    elapsed_time = current_time - renderer.feedback_animation_start_time
    total_frames = renderer.get_total_frames(emotion_level)

    if total_frames > 0:
        # 循环播放动画
        frame_index = int((elapsed_time * config.animation_fps) % total_frames)
    else:
        frame_index = 0

    # 根据成功与否设置颜色
    if success:
        fill_color = config.success_dot_color
    else:
        fill_color = config.failure_dot_color

    renderer.set_dots_color(fill_color, config.dot_line_color)

    # 更新位置并绘制动态动画
    if renderer.update_dots_positions(emotion_level, frame_index):
        renderer.draw_current_frame()

    return emotion_level

def main():
    """
    主函数，用于调试3D小人显示效果
    可以手动设置显示不同情绪等级的动画变化
    """
    from psychopy import visual, core, event, monitors

    # 设置窗口
    mon = monitors.Monitor('myMonitor', width=config.monitor_width, distance=config.monitor_distance)
    win = visual.Window(
        size=[1920, 1080],
        monitor=mon,
        winType='pyglet',
        units='pix',
        color=config.bg_color,
        fullscr=True
    )

    # 创建3D渲染器
    renderer = Walk3DRenderer(win)

    # 创建文本显示当前状态
    status_text = visual.TextStim(
        win,
        text="",
        pos=(0, 250),
        height=20,
        color=[1, 1, 1],
        font=config.font_name
    )

    # 创建说明文本
    instruction_text = visual.TextStim(
        win,
        text="按键控制:\n左右箭头: 切换情绪等级(-6到6)\n空格: 播放/暂停动画\nESC: 退出",
        pos=(0, -250),
        height=16,
        color=[1, 1, 1],
        font=config.font_name,
        wrapWidth=600
    )

    # 初始状态
    current_emotion = 0
    is_playing = True
    animation_start_time = core.getTime()

    print("3D小人调试程序启动")
    print("使用左右箭头键切换情绪等级，空格键播放/暂停，ESC退出")

    # 主循环
    while True:
        current_time = core.getTime()

        # 检查键盘输入
        keys = event.getKeys()
        for key in keys:
            if key == 'escape':
                win.close()
                core.quit()
                return
            elif key == 'left':
                current_emotion = max(-6, current_emotion - 1)
                animation_start_time = current_time
                print(f"切换到情绪等级: {current_emotion}")
            elif key == 'right':
                current_emotion = min(6, current_emotion + 1)
                animation_start_time = current_time
                print(f"切换到情绪等级: {current_emotion}")
            elif key == 'space':
                is_playing = not is_playing
                if is_playing:
                    animation_start_time = current_time
                print(f"动画播放: {'开启' if is_playing else '暂停'}")

        # 计算当前帧
        if is_playing:
            elapsed_time = current_time - animation_start_time
            total_frames = renderer.get_total_frames(current_emotion)
            if total_frames > 0:
                frame_index = int((elapsed_time * config.animation_fps) % total_frames)
            else:
                frame_index = 0
        else:
            frame_index = 0

        # 清空屏幕
        win.clearBuffer()

        # 更新状态文本
        status_text.text = f"情绪等级: {current_emotion} | 帧: {frame_index} | 播放: {'是' if is_playing else '否'}"

        # 设置颜色（根据情绪等级）
        if current_emotion < -2:
            fill_color = [0, 1, 0]  # 很开心 - 绿色
        elif current_emotion > 2:
            fill_color = [1, 0, 0]  # 很伤心 - 红色
        else:
            fill_color = config.dot_fill_color  # 中性 - 白色

        renderer.set_dots_color(fill_color, config.dot_line_color)

        # 更新并绘制3D小人
        if renderer.update_dots_positions(current_emotion, frame_index):
            renderer.draw_current_frame()

        # 绘制文本
        status_text.draw()
        instruction_text.draw()

        # 刷新屏幕
        win.flip()

        # 控制帧率
        core.wait(1.0 / 60)  # 60 FPS

if __name__ == '__main__':
    main()
