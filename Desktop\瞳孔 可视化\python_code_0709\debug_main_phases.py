# -*- coding: utf-8 -*-
"""
测试主程序中各个阶段函数的调试程序
模拟EyeLink环境来测试基线、自主调节、反馈阶段

作者: AI Assistant
日期: 2025-07-23
"""

import os
import sys
import numpy as np
import time
from psychopy import visual, core, event, monitors
from psychopy.hardware import keyboard

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

# 导入配置和模块
from config import config
from pupil_biofeedback_training import (
    PupilDataProcessor, create_visual_stimuli, 
    run_baseline_phase, run_modulation_phase, run_feedback_phase,
    setup_psychopy_window
)

class MockEyeLink:
    """模拟EyeLink追踪器"""
    def __init__(self):
        self.recording = True
        self.sample_data = []
        self.current_sample_index = 0
        
    def sendMessage(self, message):
        print(f"EyeLink消息: {message}")
        
    def isRecording(self):
        return 0 if self.recording else 1  # pylink.TRIAL_OK = 0
        
    def getNewestSample(self):
        # 返回模拟样本
        return MockSample()
        
    def eyeAvailable(self):
        return 0  # 左眼

class MockSample:
    """模拟EyeLink样本"""
    def __init__(self):
        # 模拟瞳孔大小变化（像素值）
        base_pupil = 4500  # 基线瞳孔大小（像素）
        variation = np.random.normal(0, 100)  # 添加一些随机变化
        self.pupil_size = max(1000, base_pupil + variation)
        self.timestamp = core.getTime() * 1000  # 转换为毫秒
        
    def isLeftSample(self):
        return True
        
    def isRightSample(self):
        return False
        
    def getLeftEye(self):
        return MockEye(self.pupil_size)
        
    def getRightEye(self):
        return MockEye(self.pupil_size)
        
    def getTime(self):
        return self.timestamp

class MockEye:
    """模拟眼睛数据"""
    def __init__(self, pupil_size):
        self.pupil_size = pupil_size
        
    def getPupilSize(self):
        return self.pupil_size

class MockGenv:
    """模拟图形环境"""
    def getBackgroundColor(self):
        return config.bg_color
        
    def getForegroundColor(self):
        return [1, 1, 1]  # 白色

def test_baseline_phase():
    """测试基线阶段"""
    print("\n=== 测试基线阶段函数 ===")
    
    # 设置窗口和刺激
    win, scn_width, scn_height = setup_psychopy_window()
    stimuli = create_visual_stimuli(win)
    
    # 创建模拟对象
    el_tracker = MockEyeLink()
    genv = MockGenv()
    pupil_processor = PupilDataProcessor()
    kb = keyboard.Keyboard()
    
    try:
        # 运行基线阶段
        print("开始运行基线阶段...")
        baseline_size = run_baseline_phase(el_tracker, win, genv, stimuli, pupil_processor, kb)
        print(f"基线阶段完成，基线瞳孔大小: {baseline_size:.3f}mm")
        
        return baseline_size, win, stimuli, el_tracker, genv, pupil_processor, kb
        
    except Exception as e:
        print(f"基线阶段测试错误: {e}")
        import traceback
        traceback.print_exc()
        win.close()
        return None, None, None, None, None, None, None

def test_modulation_phase(baseline_size, win, stimuli, el_tracker, genv, pupil_processor, kb):
    """测试自主调节阶段"""
    print("\n=== 测试自主调节阶段函数 ===")
    
    try:
        # 运行自主调节阶段
        print("开始运行自主调节阶段...")
        condition = "enlarge"  # 测试放大条件
        eye_used = 0  # 左眼
        
        result, modulation_data = run_modulation_phase(
            el_tracker, win, genv, stimuli, pupil_processor, 
            condition, baseline_size, kb, eye_used
        )
        
        print(f"自主调节阶段完成，结果: {result}")
        print(f"调节数据点数: {len(modulation_data)}")
        if len(modulation_data) > 0:
            print(f"瞳孔数据范围: {min(modulation_data):.3f} - {max(modulation_data):.3f}mm")
        
        return modulation_data, condition
        
    except Exception as e:
        print(f"自主调节阶段测试错误: {e}")
        import traceback
        traceback.print_exc()
        return [], "enlarge"

def test_feedback_phase(baseline_size, modulation_data, condition, win, stimuli, el_tracker, genv, kb):
    """测试反馈阶段"""
    print("\n=== 测试反馈阶段函数 ===")
    
    try:
        # 运行反馈阶段
        print("开始运行反馈阶段...")
        
        success = run_feedback_phase(
            el_tracker, win, genv, stimuli, baseline_size, 
            modulation_data, condition, kb
        )
        
        print(f"反馈阶段完成，训练结果: {'成功' if success else '失败'}")
        
        return success
        
    except Exception as e:
        print(f"反馈阶段测试错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("主程序各阶段函数调试测试")
    print("=" * 50)
    print(f"显示模式: {config.display_mode}")
    print("这个测试将调用主程序中的实际阶段函数")
    print("=" * 50)
    
    try:
        # 1. 测试基线阶段
        baseline_size, win, stimuli, el_tracker, genv, pupil_processor, kb = test_baseline_phase()
        
        if baseline_size is None:
            print("基线阶段测试失败，退出")
            return
        
        # 2. 测试自主调节阶段
        modulation_data, condition = test_modulation_phase(
            baseline_size, win, stimuli, el_tracker, genv, pupil_processor, kb
        )
        
        # 3. 测试反馈阶段
        success = test_feedback_phase(
            baseline_size, modulation_data, condition, win, stimuli, el_tracker, genv, kb
        )
        
        # 显示测试总结
        print("\n" + "=" * 50)
        print("测试总结:")
        print(f"基线瞳孔大小: {baseline_size:.3f}mm")
        print(f"调节数据点数: {len(modulation_data)}")
        print(f"训练条件: {condition}")
        print(f"最终结果: {'成功' if success else '失败'}")
        print("=" * 50)
        
        # 等待用户按键退出
        print("按任意键退出...")
        event.waitKeys()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭窗口
        if 'win' in locals() and win is not None:
            win.close()
        core.quit()

if __name__ == '__main__':
    main()
