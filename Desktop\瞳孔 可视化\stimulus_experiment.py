# -*- coding: utf-8 -*-
"""
本脚本的目的在于使用pylink库与Eyelink眼动仪进行交互，实现一个简单的视觉刺激实验。
实验流程如下：
1.  连接到Eyelink主机。
2.  设置并打开一个EDF数据文件用于记录。
3.  使用pygame进行眼动仪校准。
4.  校准后，使用tkinter创建一个全屏窗口。
5.  在屏幕上交替显示纯黑屏和纯白屏，每种颜色持续一个可调的时间，共重复数次。
6.  在每次屏幕颜色切换时，通过pylink API向EDF文件发送事件标记。
7.  实时获取瞳孔数据，记录所有黑屏时间的最小瞳孔值和所有白屏时间的最大瞳孔值。
8.  实验结束后，停止记录，在屏幕上显示结果，并将EDF数据文件从Eyelink主机传输到本地。
9.  文件名将根据实验结束时的日期和时间动态生成。
"""

import pylink
import pygame
import os
import sys
import tkinter as tk
from datetime import datetime
import threading
import time

# ##############################################################################
#            添加到系统路径以查找CalibrationGraphicsPygame
# ##############################################################################
calibration_graphics_path = os.path.join(
    'eyelink python example', 'examples', 'Pygame_examples', 'picture'
)
sys.path.append(calibration_graphics_path)
try:
    from CalibrationGraphicsPygame import CalibrationGraphics
except ImportError:
    print(f"错误: 无法从路径 '{calibration_graphics_path}' 导入 CalibrationGraphicsPygame。")
    print("请确保该文件存在，并且您的运行目录是项目的根目录。")
    sys.exit(1)


# ##############################################################################
#                             实验参数设置
# ##############################################################################

# 设置屏幕分辨率 (校准时使用)
SCN_WIDTH, SCN_HEIGHT = 800, 600

# 定义颜色
BLACK = "black"
WHITE = "white"

# 试次重复次数
N_REPETITIONS = 3

# 黑/白屏显示时间 (毫秒) - 可调参数
STIMULUS_DURATION_MS = 2000

# 设置数据文件夹
DATA_DIR = 'data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# 设置是否以虚拟模式运行（不连接真实眼动仪，用于调试）
DUMMY_MODE = False  # 改为 False 以连接真实眼动仪

# 全局变量用于存储瞳孔数据和线程控制
pupil_data_lock = threading.Lock()
min_pupil_black = float('inf')
max_pupil_white = 0.0
is_running = True
current_screen_color = None # 'black' or 'white'

def pupil_data_collection_thread(el_tracker):
    """
    一个在后台运行的线程，用于持续从眼动仪获取数据。
    """
    global min_pupil_black, max_pupil_white, is_running, current_screen_color

    while is_running:
        sample = el_tracker.getNewestSample()
        if sample is not None:
            left_pupil = 0
            right_pupil = 0
            
            # 获取瞳孔数据
            if sample.isLeftSample():
                # getPupilSize() 可能会返回 0.0，代表数据丢失
                pupil = sample.getLeftEye().getPupilSize()
                if pupil > 0:
                    left_pupil = pupil
            if sample.isRightSample():
                pupil = sample.getRightEye().getPupilSize()
                if pupil > 0:
                    right_pupil = pupil
            
            # 计算平均瞳孔值，确保只使用有效的非零值
            valid_pupils = []
            if left_pupil > 0:
                valid_pupils.append(left_pupil)
            if right_pupil > 0:
                valid_pupils.append(right_pupil)

            if not valid_pupils:
                continue
            
            avg_pupil = sum(valid_pupils) / len(valid_pupils)

            # 根据当前屏幕颜色更新最大/最小值
            with pupil_data_lock:
                if current_screen_color == 'black':
                    if avg_pupil < min_pupil_black:
                        min_pupil_black = avg_pupil
                elif current_screen_color == 'white':
                    if avg_pupil > max_pupil_white:
                        max_pupil_white = avg_pupil
        # 稍微暂停，避免CPU占用过高
        time.sleep(0.001)


def setup_and_calibrate(el_tracker):
    """
    使用Pygame进行校准的独立函数
    """
    pygame.init()
    win_pygame = pygame.display.set_mode((SCN_WIDTH, SCN_HEIGHT))
    
    # 关键：从pylink获取 CalibrationGraphics 实例
    genv = CalibrationGraphics(el_tracker, win_pygame)
    pylink.openGraphicsEx(genv)

    print("开始进行眼动仪校准... 按 'c' 开始校准, 'v' 开始验证, 'o' 退出。")
    el_tracker.doTrackerSetup()
    print("校准完成.")
    
    # 确保在退出前关闭pygame
    pylink.closeGraphics()
    pygame.quit()


def run_experiment():
    """
    主实验函数
    """
    global is_running, current_screen_color, min_pupil_black, max_pupil_white
    
    el_tracker = None
    # ##########################################################################
    #                         步骤 1: 连接Eyelink
    # ##########################################################################
    try:
        if DUMMY_MODE:
            el_tracker = pylink.EyeLink(None)
        else:
            el_tracker = pylink.EyeLink("*********")
        print("Eyelink 连接成功.")
    except RuntimeError as error:
        print(f"Eyelink 连接失败: {error}")
        return

    # ##########################################################################
    #                      步骤 2: 打开EDF文件和配置追踪器
    # ##########################################################################
    edf_filename_host = "TEST.EDF"
    try:
        el_tracker.openDataFile(edf_filename_host)
        print(f"在Eyelink主机上打开EDF文件: {edf_filename_host}")
    except RuntimeError as e:
        print(f"无法打开EDF文件: {e}")
        el_tracker.close()
        return
    
    preamble_text = f'RECORDED BY stimulus_experiment.py'
    el_tracker.sendCommand(f"add_file_preamble_text '{preamble_text}'")

    el_tracker.setOfflineMode()
    eyelink_ver = el_tracker.getTrackerVersionString()
    print(f"Eyelink版本: {eyelink_ver}")
    
    # 配置数据类型
    file_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT'
    link_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,FIXUPDATE'
    # 关键：确保我们请求了PUPIL和AREA数据
    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,AREA,PUPIL,GAZERES,STATUS,INPUT'
    link_sample_flags = 'LEFT,RIGHT,GAZE,AREA,PUPIL,GAZERES,STATUS,INPUT'
    el_tracker.sendCommand(f"file_event_filter = {file_event_flags}")
    el_tracker.sendCommand(f"link_event_filter = {link_event_flags}")
    el_tracker.sendCommand(f"file_sample_data = {file_sample_flags}")
    el_tracker.sendCommand(f"link_sample_data = {link_sample_flags}")

    el_tracker.sendCommand(f"screen_pixel_coords = 0 0 {SCN_WIDTH - 1} {SCN_HEIGHT - 1}")
    el_tracker.sendMessage(f"DISPLAY_COORDS 0 0 {SCN_WIDTH - 1} {SCN_HEIGHT - 1}")
    el_tracker.sendCommand("calibration_type = HV9")

    # ##########################################################################
    #                         步骤 3: 使用Pygame进行校准
    # ##########################################################################
    # setup_and_calibrate(el_tracker)

    # ##########################################################################
    #                       步骤 4: 初始化Tkinter窗口
    # ##########################################################################
    root = tk.Tk()
    root.attributes('-fullscreen', True)  # 设置全屏
    root.configure(bg=BLACK) # 默认背景为黑色
    root.bind("<Escape>", lambda e: root.destroy()) # 按ESC退出
    
    # 隐藏鼠标
    root.config(cursor="none")
    
    # ##########################################################################
    #                       步骤 5: 运行实验
    # ##########################################################################
    
    el_tracker.startRecording(1, 1, 1, 1)
    pylink.msecDelay(100)
    
    # 启动数据采集线程
    data_thread = threading.Thread(target=pupil_data_collection_thread, args=(el_tracker,))
    data_thread.start()

    def show_results_and_exit():
        global is_running
        
        # 停止数据采集
        is_running = False
        data_thread.join() # 等待线程结束

        # 停止记录
        print("停止记录...")
        el_tracker.stopRecording()
    
        # 在屏幕上显示结果
        root.configure(bg='grey')
        # 处理inf值，如果没记录到任何数据
        min_val_str = f"{min_pupil_black:.2f}" if min_pupil_black != float('inf') else "无有效数据"
        max_val_str = f"{max_pupil_white:.2f}" if max_pupil_white != 0.0 else "无有效数据"

        result_text = (f"实验完成!\n\n"
                       f"黑屏时最小瞳孔值: {min_val_str}\n"
                       f"白屏时最大瞳孔值: {max_val_str}\n\n"
                       f"程序将在5秒后退出。")
        label = tk.Label(root, text=result_text, font=("Arial", 24), bg='grey', fg='white')
        label.pack(expand=True)
        root.update()
        
        # 传输数据
        print("关闭主机上的EDF文件...")
        el_tracker.setOfflineMode()
        pylink.msecDelay(500)
        el_tracker.closeDataFile()
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        local_edf_filename = f"pupil_{timestamp}.edf"
        local_edf_path = os.path.join(DATA_DIR, local_edf_filename)
        
        print(f"正在从主机传输EDF文件到: {local_edf_path}")
        try:
            el_tracker.receiveDataFile(edf_filename_host, local_edf_path)
            print("文件传输成功.")
        except RuntimeError as error:
            print(f"文件传输失败: {error}")

        print("关闭与Eyelink的连接.")
        el_tracker.close()
        
        # 5秒后退出程序
        root.after(5000, root.destroy)

    def run_trial(trial_num):
        global current_screen_color
        if trial_num > N_REPETITIONS:
            show_results_and_exit()
            return
            
        # --- 显示黑屏 ---
        with pupil_data_lock:
            current_screen_color = 'black'
        root.configure(bg=BLACK)
        root.update()
        el_tracker.sendMessage(f"TRIAL_{trial_num}_BLACK_ON")
        print(f"试次 {trial_num}/{N_REPETITIONS}: 黑屏显示")
        
        # STIMULUS_DURATION_MS 毫秒后切换到白屏
        root.after(STIMULUS_DURATION_MS, lambda: switch_to_white(trial_num))

    def switch_to_white(trial_num):
        global current_screen_color
        # --- 显示白屏 ---
        with pupil_data_lock:
            current_screen_color = 'white'
        root.configure(bg=WHITE)
        root.update()
        el_tracker.sendMessage(f"TRIAL_{trial_num}_WHITE_ON")
        print(f"试次 {trial_num}/{N_REPETITIONS}: 白屏显示")
        
        # STIMULUS_DURATION_MS 毫秒后开始下一个试次
        root.after(STIMULUS_DURATION_MS, lambda: run_trial(trial_num + 1))

    # 开始第一个试次
    run_trial(1)
    
    root.mainloop()


if __name__ == '__main__':
    try:
        run_experiment()
    except Exception as e:
        print(f"实验过程中发生错误: {e}")
    finally:
        # 确保pygame和tkinter都退出
        if pygame.get_init():
            pygame.quit()
        # is_running = False # 确保线程能退出
        print("程序退出。")
        sys.exit() 