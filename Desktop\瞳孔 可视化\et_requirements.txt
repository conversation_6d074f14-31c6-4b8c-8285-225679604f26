# conda环境et的Python包列表
# 使用方法: pip install -r et_requirements.txt

anyio==3.6.2
arabic-reshaper==3.0.0
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
arrow==1.2.3
asttokens==2.2.1
astunparse==1.6.3
attrs==22.2.0
backcall==0.2.0
beautifulsoup4==4.13.4
bleach==6.0.0
blosc2==2.5.1
certifi==2025.6.15
cffi==1.17.1
chardet==4.0.0
charset-normalizer==3.4.2
colorama==0.4.6
comm==0.1.2
contourpy==1.3.0
cryptography==45.0.5
cv2_enumerate_cameras==1.2.2
cycler==0.12.1
Cython==3.1.2
debugpy==1.6.6
decorator==5.2.1
defusedxml==0.7.1
dukpy==0.2.3
elementpath==5.0.3
esprima==4.0.1
et_xmlfile==2.0.0
exceptiongroup==1.3.0
executing==1.2.0
eyelinkio==0.3.0
fastjsonschema==2.16.2
fastnumbers==5.1.1
ffpyplayer==4.5.3
fonttools==4.58.5
fqdn==1.5.1
freetype-py==2.5.1
future==1.0.0
gevent==25.5.1
gitdb==4.0.12
GitPython==3.1.44
gmsh_interop==2024.1
greenlet==3.2.3
h5py==3.14.0
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib-metadata==6.0.0
importlib_resources==6.5.2
ipykernel==6.21.1
ipython==8.18.1
ipython-genutils==0.2.0
ipywidgets==8.0.4
isoduration==20.11.0
javascripthon==0.13
jedi==0.19.2
Jinja2==3.1.2
json-tricks==3.17.3
jsonpointer==2.3
jsonschema==4.17.3
jupyter==1.0.0
jupyter-console==6.5.1
jupyter-events==0.6.3
jupyter_client==8.0.2
jupyter_core==5.2.0
jupyter_server==2.3.0
jupyter_server_terminals==0.4.4
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.5
kiwisolver==1.4.7
lazy_loader==0.4
markdown-it-py==3.0.0
MarkupSafe==2.1.2
matplotlib==3.9.4
matplotlib-inline==0.1.6
mdurl==0.1.2
MeshPy==2022.1.3
mistune==2.0.5
mne==1.8.0
moviepy==2.2.1
msgpack==1.1.1
msgpack-numpy==0.4.8
nbclassic==0.5.2
nbclient==0.7.2
nbconvert==7.2.9
nbformat==5.7.3
ndindex==1.10.0
nest-asyncio==1.5.6
notebook==6.5.2
notebook_shim==0.2.2
numexpr==2.10.2
numpy==1.26.4
opencv-python==*********
opencv-python-headless==4.12.0
openpyxl==3.1.5
packaging==25.0
pandas==2.3.1
pandocfilters==1.5.0
parso==0.8.4
patsy==1.0.1
pickleshare==0.7.5
pillow==11.3.0
pingouin==0.2.2
platformdirs==4.3.8
pooch==1.8.2
proglog==0.1.12
prometheus-client==0.16.0
prompt_toolkit==3.0.51
psutil==7.0.0
psychopy==2025.1.1
psychtoolbox==*********
pure-eval==0.2.2
py-cpuinfo==9.0.0
pyarrow==20.0.0
pycparser==2.22
pyedfread @ git+https://github.com/s-ccs/pyedfread@4a8800cc003b13075d032cbaf33f076b7ee39aaf
pygame==2.6.1
pyglet==1.4.11
Pygments==2.14.0
PyLink==0.3.3
pyparallel==0.2.2
pyparsing==3.2.3
pypillometry==*******
pypiwin32==223
PyQt6==6.9.1
PyQt6-Qt6==6.9.1
PyQt6_sip==13.10.2
pyrsistent==0.19.3
pyserial==3.5
pystan==********
python-bidi==0.6.6
python-datamatrix==*******
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-eyelinkparser==0.17.3
python-gitlab==6.1.0
python-json-logger==2.0.6
python-vlc==3.0.11115
pytools==2024.1.14
PyTrack-NTU==1.1.1
pytz==2025.2
pywin32==310
pyWinhook==1.6.2
pywinpty==2.0.10
PyYAML==6.0.2
pyzmq==27.0.0
qtconsole==5.4.0
QtPy==2.3.0
questplus==2023.1
requests==2.32.4
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
scipy==1.13.1
seaborn==0.13.2
Send2Trash==1.8.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.0
soundfile==0.13.1
soupsieve==2.4
SQLAlchemy==2.0.41
sr-research-pylink==2.1.762.0
stack-data==0.6.2
statsmodels==0.14.5
tables==3.9.1
terminado==0.17.1
tinycss2==1.4.0
tornado==6.2
tqdm==4.67.1
traitlets==5.9.0
typing==*******
typing_extensions==4.14.1
tzdata==2025.2
ujson==5.10.0
uri-template==1.2.0
urllib3==2.5.0
wcwidth==0.2.6
webcolors==1.12
webencodings==0.5.1
websocket-client==1.5.1
websockets==15.0.1
widgetsnbextension==4.0.5
wxPython==4.2.3
xarray==2024.7.0
xmlschema==4.1.0
zipp==3.23.0
zope.event==5.1
zope.interface==7.2
