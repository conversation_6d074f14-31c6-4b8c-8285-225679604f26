# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练程序配置文件
用户可以在此文件中调整实验参数

作者: AI Assistant
日期: 2025-07-09
"""

class ExperimentConfig:
    """实验配置类"""
    
    def __init__(self):
        # ==================== 基本设置 ====================
        self.dummy_mode = False  # 是否使用模拟模式（调试用）
        self.full_screen = True  # 是否全屏显示
        # self.full_screen = False  # 是否全屏显示
        self.eyelink_ip = "*********"  # EyeLink主机IP地址
        self.debug_mode = True  # 是否开启调试模式
        
        # ==================== 时间设置 (秒) ====================
        # self.instruction_duration = 3.0   # 指示语显示时长
        # self.baseline_duration = 7.0       # 基线阶段时长
        # self.modulation_duration = 15.0    # 自主调适阶段时长
        # self.feedback_duration = 2.0       # 反馈阶段时长
        # self.rest_duration = 5.0           # 休息时长
        self.instruction_duration = 0   # 指示语显示时长
        self.baseline_duration = 5.0       # 基线阶段时长
        self.modulation_duration = 15.0    # 自主调适阶段时长
        self.feedback_duration = 2.0       # 反馈阶段时长
        self.rest_duration = 5.0           # 休息时长

        # 暂停页面设置
        self.show_pause_after_trial = True  # 是否在每个trial后显示暂停页面
        self.pause_message = "按Enter键继续下一个试次"  # 暂停页面显示的消息

        # 反馈计算设置
        self.feedback_calculation_seconds = 10  # 用于计算反馈的最后N秒数据

        # 阶段提示语设置
        self.phase_prompt_duration = 2.0   # 阶段提示语显示时长
        self.show_phase_prompts = True     # 是否显示阶段提示语
        # self.phase_prompt_duration = 1   # 阶段提示语显示时长
        # self.show_phase_prompts = True     # 是否显示阶段提示语
        
        # ==================== 瞳孔参数 ====================
        # EyeLink瞳孔数据转换参数
        self.pupil_calibration_mm = 3.5    # 校准时的瞳孔直径 (mm)
        self.pupil_calibration_pixels = 4500  # 对应的像素值

        # 瞳孔有效范围 (mm)
        # self.pupil_min_size_mm = 1.5       # 最小有效瞳孔大小 (mm)
        # self.pupil_max_size_mm = 9.0       # 最大有效瞳孔大小 (mm)
        # self.max_change_rate_mm = 0.0027   # 最大变化速率 (mm/ms)
        #debug
        self.pupil_min_size_mm = 2      # 最小有效瞳孔大小 (mm)
        self.pupil_max_size_mm = 9      # 最大有效瞳孔大小 (mm)
        self.max_change_rate_mm = 27   # 最大变化速率 (mm/ms)

        # 反馈参数
        self.feedback_rate = 30            # 反馈更新频率 (Hz)
        self.samples_per_feedback = 33     # 每次反馈的样本数 (1000Hz/30Hz ≈ 33)
        
        # ==================== 实验流程 ====================
        self.trials_per_block = 10         # 每个block的试次数 10
        self.blocks_per_condition = 1      # 每个条件的block数
        
        # ==================== 显示参数 ====================
        # 显示模式选择
        self.display_mode = "3d_walk"       # 显示模式: "circle", "3d_walk" 或 "face"

        # 圆圈显示参数
        self.baseline_circle_radius = 300   # 固定基线圆圈半径 (像素) - 所有阶段的基线圆圈都是这个大小
        self.circle_line_width = 3         # 基准圆圈线宽 (用于计算总像素数)
        self.fixation_size = 50            # 注视点大小
        self.pupil_display_scale = 300     # 瞳孔差值显示比例 (像素/mm) - k*(real_pupil-base_pupil) + base_circle = real_circle

        # ==================== 3D小人显示参数 ====================
        # 3D动画文件路径
        self.walk_3d_folder = r"C:\Users\<USER>\Desktop\瞳孔 可视化\3D-walk"  # 3D动画数据文件夹路径

        # 3D投影参数
        self.viewing_angle = 45            # 观看角度 (度)，可以是45, -45等
        self.projection_distance = 2500   # 投影距离 (用于3D到2D转换)

        # 显示区域参数
        self.display_area_size = 400       # 显示区域边长 (像素)
        self.display_center_x = 0          # 显示中心X坐标 (相对于屏幕中心)
        self.display_center_y = 0          # 显示中心Y坐标 (相对于屏幕中心)

        # 3D小人圆点参数
        self.dot_radius = 8                # 圆点半径 (像素)
        self.dot_fill_color = [1, 1, 1]    # 圆点填充颜色 (白色)
        self.dot_line_color = [0, 0, 0]    # 圆点边框颜色 (黑色)
        self.dot_line_width = 1            # 圆点边框宽度

        # 动画参数
        self.animation_fps = 30            # 动画帧率 (帧/秒)，默认1秒30帧
        self.frame_duration = 1.0 / self.animation_fps  # 每帧持续时间

        # 瞳孔到情绪映射参数
        self.pupil_emotion_scale = 1     # 瞳孔映射比例系数 k，pupil = k*(实时瞳孔-基线)
        self.emotion_step_size = 0.05       # 每隔多少进入下一个情绪等级 (默认100)

        # 反馈阶段3D小人颜色
        self.success_dot_color = [0, 1, 0]  # 成功时的圆点颜色 (绿色)
        self.failure_dot_color = [1, 0, 0]  # 失败时的圆点颜色 (红色)

        # ==================== Face显示参数 ====================
        # Face笑脸参数
        self.face_scale = 500.0               # 脸部整体比例系数 (1.0为默认大小，可以调整放大缩小)
        self.face_head_radius = 0.45        # 头部半径 (相对窗口高度)
        self.face_eye_fraction = 1/3        # 眼睛水平直径占脸直径的比例
        self.face_eye_vertical_ratio = 0.60 # 眼睛垂直/水平半径比例 (形成扁椭圆)
        self.face_eye_dx = 0.22             # 左右眼中心水平偏移
        self.face_eye_dy = 0.16             # 眼睛中心垂直偏移 (往上)
        self.face_pupil_ratio = 0.55        # 瞳孔半径占眼睛垂直半径的比例
        self.face_mouth_y = -0.20           # 嘴巴位置 (往下)
        self.face_mouth_half_width = 0.25   # 嘴半宽
        self.face_nose_size = 0.03          # 鼻子大小 (正方形边长)
        self.face_nose_y = 0.02             # 鼻子位置 (往下偏移)
        self.face_line_width = 3            # 线条宽度

        # Face瞳孔移动参数
        self.face_pupil_scale = 150         # 瞳孔移动比例系数 (像素/mm)
        self.face_pupil_direction = 1       # 瞳孔大时的移动方向 (1为右，-1为左)
        self.face_pupil_left_limit = 0.0    # 瞳孔向左移动的最大范围 (0=中心位置, 1=最大范围)
        self.face_pupil_right_limit = 1.0   # 瞳孔向右移动的最大范围 (0=中心位置, 1=最大范围)

        # Face颜色参数
        self.face_default_color = [0, 0, 0] # 默认黑色
        self.face_success_color = [0, 1, 0] # 成功时的绿色
        self.face_failure_color = [1, 0, 0] # 失败时的红色
        
        # ==================== 颜色设置 ====================
        # 背景颜色：灰色 (150, 150, 150) 转换为PsychoPy范围 (-1到1)
        self.bg_color = [150/255*2-1, 150/255*2-1, 150/255*2-1]

        # 注视点颜色：白色
        self.fixation_color = [1, 1, 1]

        # 计算亮度值与灰色一致 (Y = 0.2126*R + 0.7152*G + 0.0722*B)
        gray_luminance = 0.2126*150 + 0.7152*150 + 0.0722*150  # 150

        # 计算绿色值（亮度与灰色一致）
        green_g = gray_luminance / 0.7152  # 约209.8

        # 对于蓝色，由于系数很小，直接计算会超过255
        # 使用一个合理的蓝色值，然后调整其他分量来匹配亮度
        # 选择蓝色值为255，然后计算需要的红色和绿色分量
        blue_b = 255
        # Y = 0.2126*R + 0.7152*G + 0.0722*B = 150
        # 0.2126*R + 0.7152*G = 150 - 0.0722*255 = 150 - 18.411 = 131.589
        # 为了简化，设R=G，则 (0.2126 + 0.7152)*R = 131.589
        # R = G = 131.589 / 0.9278 = 141.8
        blue_rg = 131.589 / (0.2126 + 0.7152)  # 约141.8

        # 基线圆圈颜色：蓝色（亮度与灰色一致）
        # self.baseline_color = [blue_rg/255*2-1, blue_rg/255*2-1, blue_b/255*2-1]
        self.baseline_color = [0, 161 /255*2-1, 217 /255*2-1]

        # 实时反馈圆圈颜色：绿色（亮度与灰色一致）
        # 纯绿色：只有G分量，R=B=0
        green_pure = gray_luminance / 0.7152  # 约209.8
        self.feedback_color = [0, 175 /255*2-1, 0 /255*2-1]

        # 成功和失败反馈颜色
        self.success_color = [0, green_pure/255*2-1, 0]      # 绿色
        # 红色：只有R分量，G=B=0
        red_pure = gray_luminance / 0.2126  # 约705.6，需要限制
        red_pure = min(red_pure, 255)
        self.failure_color = [red_pure/255*2-1, 0, 0]      # 红色
        
        # ==================== 字体设置 ====================
        self.font_name = 'SimHei'          # 中文字体
        self.font_size = 30                # 字体大小
        self.text_wrap_width = 600         # 文本换行宽度

        # ==================== 阶段提示语文本 ====================
        self.baseline_prompt = "下面将进入基线阶段"
        self.modulation_prompt_enlarge = "下面将进入自主调适阶段\n请尝试放大瞳孔"
        self.modulation_prompt_shrink = "下面将进入自主调适阶段\n请尝试缩小瞳孔"
        self.feedback_prompt = "下面将进入反馈阶段"
        
        # ==================== 数据保存 ====================
        self.results_folder = 'results'    # 结果保存文件夹
        self.save_txt_data = False         # 是否保存额外的TXT数据文件
        
        # ==================== 高级设置 ====================
        self.sampling_rate = 1000          # EyeLink采样率 (Hz)
        self.calibration_type = "HV9"      # 校准类型
        self.validation_enabled = True     # 是否启用验证
        self.drift_check_enabled = False    # 是否启用漂移校正
        
        # 监视器设置
        self.monitor_width = 53.0          # 监视器宽度 (cm)
        self.monitor_distance = 70.0       # 观看距离 (cm)
        
    def validate_config(self):
        """验证配置参数的有效性"""
        errors = []
        
        # 检查时间参数
        if self.instruction_duration < 0:
            errors.append("指示语时长不能小于0")
        if self.baseline_duration <= 0:
            errors.append("基线阶段时长必须大于0")
        if self.modulation_duration <= 0:
            errors.append("自主调适阶段时长必须大于0")
        if self.feedback_duration <= 0:
            errors.append("反馈阶段时长必须大于0")
        if self.phase_prompt_duration <= 0:
            errors.append("阶段提示语时长必须大于0")
        if self.feedback_calculation_seconds <= 0:
            errors.append("反馈计算时长必须大于0")
        if self.feedback_calculation_seconds > self.modulation_duration:
            errors.append("反馈计算时长不能超过自主调适阶段时长")
        
        # 检查瞳孔参数
        if self.pupil_min_size_mm >= self.pupil_max_size_mm:
            errors.append("最小瞳孔大小必须小于最大瞳孔大小")
        if self.max_change_rate_mm <= 0:
            errors.append("最大变化速率必须大于0")
        if self.pupil_calibration_mm <= 0 or self.pupil_calibration_pixels <= 0:
            errors.append("瞳孔校准参数必须大于0")
        if self.feedback_rate <= 0:
            errors.append("反馈频率必须大于0")
        
        # 检查实验流程参数
        if self.trials_per_block <= 0:
            errors.append("每block试次数必须大于0")
        if self.blocks_per_condition <= 0:
            errors.append("每条件block数必须大于0")
        
        # 检查显示参数
        if self.display_mode not in ["circle", "3d_walk", "face"]:
            errors.append("显示模式必须是 'circle', '3d_walk' 或 'face'")
        if self.baseline_circle_radius <= 0:
            errors.append("基线圆圈半径必须大于0")
        if self.circle_line_width <= 0:
            errors.append("圆圈线宽必须大于0")

        # 检查3D显示参数
        if self.display_mode == "3d_walk":
            if self.display_area_size <= 0:
                errors.append("显示区域大小必须大于0")
            if self.dot_radius <= 0:
                errors.append("圆点半径必须大于0")
            if self.animation_fps <= 0:
                errors.append("动画帧率必须大于0")
            if self.emotion_step_size <= 0:
                errors.append("情绪步长必须大于0")

        # 检查Face显示参数
        if self.display_mode == "face":
            if self.face_scale <= 0:
                errors.append("Face整体比例系数必须大于0")
            if self.face_head_radius <= 0:
                errors.append("Face头部半径必须大于0")
            if self.face_eye_fraction <= 0 or self.face_eye_fraction >= 1:
                errors.append("Face眼睛比例必须在0到1之间")
            if self.face_pupil_scale <= 0:
                errors.append("Face瞳孔移动比例系数必须大于0")
            if self.face_pupil_direction not in [-1, 1]:
                errors.append("Face瞳孔移动方向必须是1或-1")
            if self.face_pupil_left_limit < 0 or self.face_pupil_left_limit > 1:
                errors.append("Face瞳孔左移范围必须在0到1之间")
            if self.face_pupil_right_limit < 0 or self.face_pupil_right_limit > 1:
                errors.append("Face瞳孔右移范围必须在0到1之间")

        return errors
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 50)
        print("当前实验配置:")
        print("=" * 50)
        print(f"基本设置:")
        print(f"  模拟模式: {self.dummy_mode}")
        print(f"  全屏显示: {self.full_screen}")
        print(f"  EyeLink IP: {self.eyelink_ip}")
        print()
        print(f"时间设置:")
        print(f"  指示语时长: {self.instruction_duration}秒")
        print(f"  基线阶段时长: {self.baseline_duration}秒")
        print(f"  自主调适阶段时长: {self.modulation_duration}秒")
        print(f"  反馈阶段时长: {self.feedback_duration}秒")
        print(f"  休息时长: {self.rest_duration}秒")
        print(f"  阶段提示语时长: {self.phase_prompt_duration}秒")
        print(f"  显示阶段提示语: {self.show_phase_prompts}")
        print(f"  试次后暂停: {self.show_pause_after_trial}")
        print(f"  反馈计算时长: {self.feedback_calculation_seconds}秒")
        print()
        print(f"瞳孔参数:")
        print(f"  瞳孔校准: {self.pupil_calibration_mm}mm = {self.pupil_calibration_pixels}像素")
        print(f"  有效瞳孔大小范围: {self.pupil_min_size_mm}-{self.pupil_max_size_mm}mm")
        print(f"  最大变化速率: {self.max_change_rate_mm}mm/ms")
        print(f"  反馈频率: {self.feedback_rate}Hz")
        print()
        print(f"实验流程:")
        print(f"  每block试次数: {self.trials_per_block}")
        print(f"  每条件block数: {self.blocks_per_condition}")
        print(f"  总试次数: {self.trials_per_block * self.blocks_per_condition * 2}")
        print()
        print(f"显示设置:")
        print(f"  显示模式: {self.display_mode}")
        if self.display_mode == "3d_walk":
            print(f"  3D动画文件夹: {self.walk_3d_folder}")
            print(f"  观看角度: {self.viewing_angle}度")
            print(f"  显示区域大小: {self.display_area_size}像素")
            print(f"  圆点半径: {self.dot_radius}像素")
            print(f"  动画帧率: {self.animation_fps}FPS")
            print(f"  瞳孔映射系数: {self.pupil_emotion_scale}")
            print(f"  情绪步长: {self.emotion_step_size}")
        elif self.display_mode == "face":
            print(f"  Face整体比例: {self.face_scale}")
            print(f"  Face头部半径: {self.face_head_radius}")
            print(f"  Face眼睛比例: {self.face_eye_fraction}")
            print(f"  Face瞳孔移动比例: {self.face_pupil_scale}")
            print(f"  Face瞳孔移动方向: {self.face_pupil_direction}")
            print(f"  Face瞳孔左移范围: {self.face_pupil_left_limit}")
            print(f"  Face瞳孔右移范围: {self.face_pupil_right_limit}")
        print("=" * 50)

# 创建默认配置实例
config = ExperimentConfig()

# 如果直接运行此文件，显示配置信息
if __name__ == '__main__':
    config.print_config()
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n配置验证通过!")
