# -*- coding: utf-8 -*-
"""
Walk3D渲染器调试测试程序
对Walk3DRenderer类的每个函数进行详细测试
用于诊断3D小人不显示白点的问题

作者: AI Assistant
日期: 2025-07-23
"""

import os
import sys
import numpy as np
import math
from psychopy import visual, core, monitors

# 添加python_code_0709到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python_code_0709'))

from config import config
from walk_3d import Walk3DRenderer

class Walk3DDebugger:
    """Walk3D调试器"""
    
    def __init__(self):
        """初始化调试器"""
        self.test_results = {}
        self.win = None
        self.renderer = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("=" * 60)
        print("设置测试环境...")
        
        # 创建测试窗口
        mon = monitors.Monitor('testMonitor', width=config.monitor_width, distance=config.monitor_distance)
        self.win = visual.Window(
            size=[800, 600],  # 使用较小窗口便于调试
            monitor=mon,
            winType='pyglet',
            units='pix',
            color=config.bg_color,
            fullscr=False  # 调试时不全屏
        )
        
        print(f"✓ 创建测试窗口: 800x600")
        print(f"✓ 背景颜色: {config.bg_color}")
        
        # 创建渲染器
        try:
            self.renderer = Walk3DRenderer(self.win)
            print(f"✓ 创建Walk3DRenderer成功")
        except Exception as e:
            print(f"✗ 创建Walk3DRenderer失败: {e}")
            return False
            
        return True
    
    def test_config_parameters(self):
        """测试配置参数"""
        print("\n" + "=" * 60)
        print("测试1: 配置参数检查")
        print("=" * 60)
        
        # 检查关键配置参数
        tests = [
            ("3D动画文件夹路径", config.walk_3d_folder, os.path.exists(config.walk_3d_folder)),
            ("圆点半径", config.dot_radius, config.dot_radius > 0),
            ("圆点填充颜色", config.dot_fill_color, len(config.dot_fill_color) == 3),
            ("圆点边框颜色", config.dot_line_color, len(config.dot_line_color) == 3),
            ("显示区域大小", config.display_area_size, config.display_area_size > 0),
            ("投影距离", config.projection_distance, config.projection_distance > 0),
            ("动画帧率", config.animation_fps, config.animation_fps > 0),
        ]
        
        all_passed = True
        for name, value, condition in tests:
            status = "✓" if condition else "✗"
            print(f"{status} {name}: {value}")
            if not condition:
                all_passed = False
        
        self.test_results['config_parameters'] = all_passed
        return all_passed
    
    def test_animation_data_loading(self):
        """测试动画数据加载"""
        print("\n" + "=" * 60)
        print("测试2: 动画数据加载")
        print("=" * 60)
        
        # 检查动画数据文件
        emotion_levels = list(range(-6, 7))
        loaded_files = 0
        
        for level in emotion_levels:
            file_path = os.path.join(config.walk_3d_folder, f"{level}.txt")
            exists = os.path.exists(file_path)
            status = "✓" if exists else "✗"
            print(f"{status} 情绪等级 {level:2d}: {file_path}")
            if exists:
                loaded_files += 1
        
        print(f"\n总计: {loaded_files}/{len(emotion_levels)} 个文件存在")
        
        # 检查渲染器中的动画数据
        if self.renderer:
            loaded_data = len(self.renderer.animation_data)
            print(f"渲染器加载的动画数据: {loaded_data} 个情绪等级")
            
            # 检查每个加载的动画数据
            for level, frames in self.renderer.animation_data.items():
                frame_count = len(frames)
                print(f"  情绪等级 {level:2d}: {frame_count} 帧")
                
                # 检查第一帧数据结构
                if frame_count > 0:
                    first_frame = frames[0]
                    if len(first_frame) == 15:
                        print(f"    ✓ 第一帧包含15个身体部位")
                        # 检查第一个身体部位的坐标
                        if len(first_frame[0]) == 3:
                            print(f"    ✓ 身体部位包含3D坐标: {first_frame[0]}")
                        else:
                            print(f"    ✗ 身体部位坐标错误: {first_frame[0]}")
                    else:
                        print(f"    ✗ 第一帧身体部位数量错误: {len(first_frame)}")
        
        success = loaded_files > 0 and self.renderer and len(self.renderer.animation_data) > 0
        self.test_results['animation_data_loading'] = success
        return success
    
    def test_3d_to_2d_projection(self):
        """测试3D到2D投影"""
        print("\n" + "=" * 60)
        print("测试3: 3D到2D投影")
        print("=" * 60)
        
        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False
        
        # 测试用例：不同的3D坐标
        test_cases = [
            (0, 0, 0, "原点"),
            (100, 100, 0, "正X正Y"),
            (-100, -100, 0, "负X负Y"),
            (0, 0, 100, "正Z"),
            (0, 0, -100, "负Z"),
            (500, 300, -200, "复杂坐标"),
        ]
        
        all_passed = True
        for x, y, z, desc in test_cases:
            try:
                screen_x, screen_y = self.renderer.project_3d_to_2d(x, y, z)
                print(f"✓ {desc} ({x}, {y}, {z}) -> ({screen_x:.1f}, {screen_y:.1f})")
                
                # 检查投影结果是否合理
                if abs(screen_x) > 10000 or abs(screen_y) > 10000:
                    print(f"  ⚠ 投影结果可能过大")
                    
            except Exception as e:
                print(f"✗ {desc} ({x}, {y}, {z}) -> 错误: {e}")
                all_passed = False
        
        self.test_results['3d_to_2d_projection'] = all_passed
        return all_passed
    
    def test_dots_creation(self):
        """测试圆点创建"""
        print("\n" + "=" * 60)
        print("测试4: 圆点对象创建")
        print("=" * 60)
        
        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False
        
        # 检查圆点数量
        dot_count = len(self.renderer.dots)
        expected_count = 15
        
        if dot_count == expected_count:
            print(f"✓ 圆点数量正确: {dot_count}")
        else:
            print(f"✗ 圆点数量错误: 期望{expected_count}, 实际{dot_count}")
            return False
        
        # 检查每个圆点的属性
        all_passed = True
        for i, dot in enumerate(self.renderer.dots):
            try:
                # 检查圆点属性
                radius = dot.radius
                fill_color = dot.fillColor
                line_color = dot.lineColor
                line_width = dot.lineWidth
                pos = dot.pos
                
                print(f"圆点 {i:2d}: 半径={radius}, 位置={pos}, 填充={fill_color}, 边框={line_color}")
                
                # 验证属性值
                if radius != config.dot_radius:
                    print(f"  ⚠ 半径不匹配: 期望{config.dot_radius}, 实际{radius}")
                
            except Exception as e:
                print(f"✗ 圆点 {i} 属性检查失败: {e}")
                all_passed = False
        
        self.test_results['dots_creation'] = all_passed
        return all_passed

    def test_pupil_to_emotion_mapping(self):
        """测试瞳孔到情绪等级映射"""
        print("\n" + "=" * 60)
        print("测试5: 瞳孔到情绪等级映射")
        print("=" * 60)

        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False

        # 测试用例：不同的瞳孔大小
        baseline_pupil = 4.0  # 基线瞳孔4mm
        test_cases = [
            (4.0, 0, "基线瞳孔"),
            (5.0, "负值", "瞳孔放大1mm"),
            (3.0, "正值", "瞳孔缩小1mm"),
            (6.0, "负值", "瞳孔放大2mm"),
            (2.0, "正值", "瞳孔缩小2mm"),
            (1.5, "正值", "最小瞳孔"),
            (9.0, "负值", "最大瞳孔"),
        ]

        all_passed = True
        for current_pupil, expected_sign, desc in test_cases:
            try:
                emotion_level = self.renderer.pupil_to_emotion_level(current_pupil, baseline_pupil)
                print(f"✓ {desc}: {current_pupil}mm -> 情绪等级 {emotion_level}")

                # 检查映射逻辑
                pupil_diff = current_pupil - baseline_pupil
                if pupil_diff > 0 and expected_sign == "负值":
                    if emotion_level >= 0:
                        print(f"  ⚠ 瞳孔放大应该对应负情绪等级，实际: {emotion_level}")
                elif pupil_diff < 0 and expected_sign == "正值":
                    if emotion_level <= 0:
                        print(f"  ⚠ 瞳孔缩小应该对应正情绪等级，实际: {emotion_level}")
                elif pupil_diff == 0 and expected_sign == 0:
                    if emotion_level != 0:
                        print(f"  ⚠ 基线瞳孔应该对应0情绪等级，实际: {emotion_level}")

                # 检查范围
                if emotion_level < -6 or emotion_level > 6:
                    print(f"  ✗ 情绪等级超出范围[-6, 6]: {emotion_level}")
                    all_passed = False

            except Exception as e:
                print(f"✗ {desc} 映射失败: {e}")
                all_passed = False

        self.test_results['pupil_to_emotion_mapping'] = all_passed
        return all_passed

    def test_frame_data_retrieval(self):
        """测试帧数据获取"""
        print("\n" + "=" * 60)
        print("测试6: 帧数据获取")
        print("=" * 60)

        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False

        all_passed = True

        # 测试每个情绪等级的帧数据
        for emotion_level in range(-6, 7):
            total_frames = self.renderer.get_total_frames(emotion_level)
            print(f"情绪等级 {emotion_level:2d}: {total_frames} 帧")

            if total_frames > 0:
                # 测试获取第一帧
                frame_data = self.renderer.get_frame_data(emotion_level, 0)
                if frame_data is not None:
                    print(f"  ✓ 第一帧数据: {len(frame_data)} 个身体部位")
                    if len(frame_data) == 15:
                        # 检查第一个身体部位的坐标
                        first_part = frame_data[0]
                        if len(first_part) == 3:
                            print(f"    ✓ 第一个身体部位坐标: ({first_part[0]:.1f}, {first_part[1]:.1f}, {first_part[2]:.1f})")
                        else:
                            print(f"    ✗ 身体部位坐标维度错误: {len(first_part)}")
                            all_passed = False
                    else:
                        print(f"    ✗ 身体部位数量错误: {len(frame_data)}")
                        all_passed = False
                else:
                    print(f"  ✗ 无法获取第一帧数据")
                    all_passed = False

                # 测试获取最后一帧
                if total_frames > 1:
                    last_frame_data = self.renderer.get_frame_data(emotion_level, total_frames - 1)
                    if last_frame_data is not None:
                        print(f"  ✓ 最后一帧数据: {len(last_frame_data)} 个身体部位")
                    else:
                        print(f"  ✗ 无法获取最后一帧数据")
                        all_passed = False

                # 测试超出范围的帧索引
                invalid_frame_data = self.renderer.get_frame_data(emotion_level, total_frames)
                if invalid_frame_data is None:
                    print(f"  ✓ 超出范围的帧索引正确返回None")
                else:
                    print(f"  ✗ 超出范围的帧索引应该返回None")
                    all_passed = False
            else:
                print(f"  ⚠ 该情绪等级没有动画数据")

        self.test_results['frame_data_retrieval'] = all_passed
        return all_passed

    def test_dots_position_update(self):
        """测试圆点位置更新"""
        print("\n" + "=" * 60)
        print("测试7: 圆点位置更新")
        print("=" * 60)

        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False

        all_passed = True

        # 找一个有数据的情绪等级进行测试
        test_emotion = None
        for emotion_level in range(-6, 7):
            if self.renderer.get_total_frames(emotion_level) > 0:
                test_emotion = emotion_level
                break

        if test_emotion is None:
            print("✗ 没有找到可用的动画数据")
            return False

        print(f"使用情绪等级 {test_emotion} 进行测试")

        # 记录更新前的位置
        old_positions = [dot.pos for dot in self.renderer.dots]
        print(f"更新前圆点位置: {old_positions[:3]}...")  # 只显示前3个

        # 更新圆点位置
        try:
            success = self.renderer.update_dots_positions(test_emotion, 0)
            if success:
                print("✓ 圆点位置更新成功")

                # 检查更新后的位置
                new_positions = [dot.pos for dot in self.renderer.dots]
                print(f"更新后圆点位置: {new_positions[:3]}...")  # 只显示前3个

                # 检查位置是否发生变化 - 使用numpy数组比较
                position_changed = False
                for i, (old_pos, new_pos) in enumerate(zip(old_positions, new_positions)):
                    if not np.array_equal(old_pos, new_pos):
                        position_changed = True
                        print(f"  圆点 {i}: {old_pos} -> {new_pos}")
                        break

                if position_changed:
                    print("✓ 圆点位置确实发生了变化")
                else:
                    print("⚠ 圆点位置没有变化（可能是数据问题）")

            else:
                print("✗ 圆点位置更新失败")
                all_passed = False

        except Exception as e:
            print(f"✗ 圆点位置更新异常: {e}")
            all_passed = False

        self.test_results['dots_position_update'] = all_passed
        return all_passed

    def test_dots_color_setting(self):
        """测试圆点颜色设置"""
        print("\n" + "=" * 60)
        print("测试8: 圆点颜色设置")
        print("=" * 60)

        if not self.renderer:
            print("✗ 渲染器未初始化")
            return False

        all_passed = True

        # 测试不同颜色设置
        test_colors = [
            ([1, 1, 1], [0, 0, 0], "白色填充，黑色边框"),
            ([1, 0, 0], [1, 1, 1], "红色填充，白色边框"),
            ([0, 1, 0], None, "绿色填充，默认边框"),
            ([0, 0, 1], [0.5, 0.5, 0.5], "蓝色填充，灰色边框"),
        ]

        for fill_color, line_color, desc in test_colors:
            try:
                # 设置颜色
                self.renderer.set_dots_color(fill_color, line_color)
                print(f"✓ {desc}")

                # 检查所有圆点的颜色是否正确设置
                for i, dot in enumerate(self.renderer.dots):
                    actual_fill = dot.fillColor
                    actual_line = dot.lineColor

                    # 检查填充颜色 - 使用numpy数组比较
                    if not np.array_equal(actual_fill, fill_color):
                        print(f"  ✗ 圆点 {i} 填充颜色错误: 期望{fill_color}, 实际{actual_fill}")
                        all_passed = False
                        break

                    # 检查边框颜色
                    expected_line = line_color if line_color is not None else config.dot_line_color
                    if not np.array_equal(actual_line, expected_line):
                        print(f"  ✗ 圆点 {i} 边框颜色错误: 期望{expected_line}, 实际{actual_line}")
                        all_passed = False
                        break

                if all_passed:
                    print(f"  ✓ 所有圆点颜色设置正确")

            except Exception as e:
                print(f"✗ {desc} 颜色设置失败: {e}")
                all_passed = False

        self.test_results['dots_color_setting'] = all_passed
        return all_passed

    def test_visual_rendering(self):
        """测试可视化渲染"""
        print("\n" + "=" * 60)
        print("测试9: 可视化渲染测试")
        print("=" * 60)

        if not self.renderer or not self.win:
            print("✗ 渲染器或窗口未初始化")
            return False

        all_passed = True

        # 找一个有数据的情绪等级
        test_emotion = None
        for emotion_level in range(-6, 7):
            if self.renderer.get_total_frames(emotion_level) > 0:
                test_emotion = emotion_level
                break

        if test_emotion is None:
            print("✗ 没有找到可用的动画数据")
            return False

        print(f"使用情绪等级 {test_emotion} 进行渲染测试")

        try:
            # 设置圆点颜色为明显的白色
            self.renderer.set_dots_color([1, 1, 1], [0, 0, 0])
            print("✓ 设置圆点为白色")

            # 更新圆点位置
            success = self.renderer.update_dots_positions(test_emotion, 0)
            if success:
                print("✓ 更新圆点位置成功")

                # 检查圆点位置是否在屏幕范围内
                screen_width = self.win.size[0]
                screen_height = self.win.size[1]

                visible_dots = 0
                for i, dot in enumerate(self.renderer.dots):
                    x, y = dot.pos
                    # PsychoPy坐标系：中心为(0,0)，范围大约是[-width/2, width/2], [-height/2, height/2]
                    if abs(x) <= screen_width/2 and abs(y) <= screen_height/2:
                        visible_dots += 1
                        print(f"  圆点 {i:2d}: 位置({x:6.1f}, {y:6.1f}) - 可见")
                    else:
                        print(f"  圆点 {i:2d}: 位置({x:6.1f}, {y:6.1f}) - 超出屏幕")

                print(f"✓ 可见圆点数量: {visible_dots}/15")

                if visible_dots == 0:
                    print("✗ 没有圆点在屏幕可见范围内！")
                    all_passed = False

                # 尝试绘制一帧
                self.win.clearBuffer()
                self.renderer.draw_current_frame()
                self.win.flip()
                print("✓ 绘制一帧完成")

                # 等待用户确认
                print("请查看窗口是否显示白点，按任意键继续...")
                core.wait(2.0)  # 等待2秒让用户观察

            else:
                print("✗ 更新圆点位置失败")
                all_passed = False

        except Exception as e:
            print(f"✗ 渲染测试异常: {e}")
            all_passed = False

        self.test_results['visual_rendering'] = all_passed
        return all_passed

    def test_interface_functions(self):
        """测试接口函数"""
        print("\n" + "=" * 60)
        print("测试10: 接口函数测试")
        print("=" * 60)

        if not self.renderer or not self.win:
            print("✗ 渲染器或窗口未初始化")
            return False

        all_passed = True

        # 导入接口函数
        from walk_3d import show_3d_baseline, show_3d_modulation, show_3d_feedback

        try:
            # 测试基线显示
            print("测试基线显示函数...")
            baseline_pupil = 4.0
            result = show_3d_baseline(self.win, self.renderer, baseline_pupil)
            if result:
                print("✓ show_3d_baseline 执行成功")
            else:
                print("✗ show_3d_baseline 执行失败")
                all_passed = False

            # 测试自主调适显示
            print("测试自主调适显示函数...")
            current_pupil = 5.0
            emotion_level = show_3d_modulation(self.win, self.renderer, current_pupil, baseline_pupil)
            print(f"✓ show_3d_modulation 返回情绪等级: {emotion_level}")

            # 测试反馈显示
            print("测试反馈显示函数...")
            best_effect_pupil = 5.5
            success = True
            feedback_emotion = show_3d_feedback(self.win, self.renderer, best_effect_pupil, baseline_pupil, success)
            print(f"✓ show_3d_feedback 返回情绪等级: {feedback_emotion}")

        except Exception as e:
            print(f"✗ 接口函数测试异常: {e}")
            all_passed = False

        self.test_results['interface_functions'] = all_passed
        return all_passed

    def run_all_tests(self):
        """运行所有测试"""
        print("开始Walk3D渲染器调试测试...")

        # 设置测试环境
        if not self.setup_test_environment():
            print("✗ 测试环境设置失败")
            return

        # 运行所有测试
        tests = [
            self.test_config_parameters,
            self.test_animation_data_loading,
            self.test_3d_to_2d_projection,
            self.test_dots_creation,
            self.test_pupil_to_emotion_mapping,
            self.test_frame_data_retrieval,
            self.test_dots_position_update,
            self.test_dots_color_setting,
            self.test_visual_rendering,
            self.test_interface_functions,
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"✗ 测试 {test_func.__name__} 异常: {e}")

        # 输出测试总结
        self.print_test_summary(passed_tests, total_tests)

        # 清理
        if self.win:
            self.win.close()

    def print_test_summary(self, passed_tests, total_tests):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)

        for test_name, result in self.test_results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{status} {test_name}")

        print(f"\n总计: {passed_tests}/{total_tests} 个测试通过")

        if passed_tests == total_tests:
            print("🎉 所有测试通过！")
        else:
            print("⚠ 存在测试失败，请检查上述详细信息")

        # 针对性建议
        if not self.test_results.get('animation_data_loading', True):
            print("\n建议: 检查3D动画数据文件是否存在且格式正确")

        if not self.test_results.get('visual_rendering', True):
            print("\n建议: 检查圆点位置计算和屏幕坐标转换")

        if not self.test_results.get('dots_creation', True):
            print("\n建议: 检查PsychoPy圆点对象创建参数")

def main():
    """主函数"""
    debugger = Walk3DDebugger()
    debugger.run_all_tests()

if __name__ == '__main__':
    main()
