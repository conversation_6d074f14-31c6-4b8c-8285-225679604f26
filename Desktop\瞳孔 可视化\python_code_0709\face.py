# -*- coding: utf-8 -*-
"""
Face笑脸显示模块
用于瞳孔生物反馈训练中的笑脸可视化
基于PsychoPy绘制简笔画笑脸，瞳孔根据瞳孔大小差值左右移动

作者: AI Assistant
日期: 2025-07-23
"""

import os
import sys
import numpy as np
import math
from psychopy import visual, core
from config import config

class FaceRenderer:
    """笑脸渲染器"""
    
    def __init__(self, win):
        """
        初始化笑脸渲染器
        
        参数:
        - win: PsychoPy窗口对象
        """
        self.win = win
        self.face_components = {}
        
        # 计算笑脸各部分的尺寸
        self.calculate_face_dimensions()
        
        # 创建笑脸组件
        self.create_face_components()
    
    def calculate_face_dimensions(self):
        """计算笑脸各部分的尺寸"""
        # 基于配置计算各部分尺寸，应用整体比例系数
        self.head_radius = config.face_head_radius * config.face_scale
        self.face_diameter = self.head_radius * 2

        # 眼睛尺寸
        self.eye_rx = self.face_diameter * config.face_eye_fraction / 2  # 椭圆水平半径
        self.eye_ry = self.eye_rx * config.face_eye_vertical_ratio       # 椭圆垂直半径

        # 瞳孔尺寸
        self.pupil_radius = self.eye_ry * config.face_pupil_ratio

        # 计算瞳孔最大移动距离（不能超出眼睛边界）
        self.max_pupil_shift = self.eye_rx - self.pupil_radius
    
    def ellipse_vertices(self, rx, ry, n=128):
        """生成椭圆顶点"""
        theta = np.linspace(0, 2*np.pi, n, endpoint=False)
        return np.column_stack([rx*np.cos(theta), ry*np.sin(theta)])
    
    def create_face_components(self):
        """创建笑脸的各个组件"""
        # 计算应用比例系数后的位置参数
        scaled_eye_dx = config.face_eye_dx * config.face_scale
        scaled_eye_dy = config.face_eye_dy * config.face_scale
        scaled_mouth_y = config.face_mouth_y * config.face_scale
        scaled_mouth_half_width = config.face_mouth_half_width * config.face_scale
        scaled_nose_size = config.face_nose_size * config.face_scale
        scaled_nose_y = config.face_nose_y * config.face_scale

        # 头部圆圈
        self.face_components['head'] = visual.Circle(
            win=self.win,
            radius=self.head_radius,
            lineColor=config.face_default_color,
            fillColor=None,
            pos=(0, 0),
            lineWidth=config.face_line_width
        )

        # 左眼椭圆
        eye_vertices = self.ellipse_vertices(self.eye_rx, self.eye_ry)
        self.face_components['eye_left'] = visual.ShapeStim(
            win=self.win,
            vertices=eye_vertices,
            closeShape=True,
            lineColor=config.face_default_color,
            fillColor=None,
            pos=(-scaled_eye_dx, scaled_eye_dy),
            lineWidth=config.face_line_width
        )

        # 右眼椭圆
        self.face_components['eye_right'] = visual.ShapeStim(
            win=self.win,
            vertices=eye_vertices,
            closeShape=True,
            lineColor=config.face_default_color,
            fillColor=None,
            pos=(scaled_eye_dx, scaled_eye_dy),
            lineWidth=config.face_line_width
        )

        # 左瞳孔
        self.face_components['pupil_left'] = visual.Circle(
            win=self.win,
            radius=self.pupil_radius,
            lineColor=config.face_default_color,
            fillColor=config.face_default_color,
            pos=(-scaled_eye_dx, scaled_eye_dy)
        )

        # 右瞳孔
        self.face_components['pupil_right'] = visual.Circle(
            win=self.win,
            radius=self.pupil_radius,
            lineColor=config.face_default_color,
            fillColor=config.face_default_color,
            pos=(scaled_eye_dx, scaled_eye_dy)
        )

        # 鼻子（小方块）
        self.face_components['nose'] = visual.Rect(
            win=self.win,
            width=scaled_nose_size,
            height=scaled_nose_size,
            lineColor=config.face_default_color,
            fillColor=config.face_default_color,
            pos=(0, scaled_nose_y)
        )

        # 嘴巴
        self.face_components['mouth'] = visual.Line(
            win=self.win,
            start=(-scaled_mouth_half_width, scaled_mouth_y),
            end=(scaled_mouth_half_width, scaled_mouth_y),
            lineWidth=5,
            lineColor=config.face_default_color
        )

        # 保存缩放后的位置参数供后续使用
        self.scaled_eye_dx = scaled_eye_dx
        self.scaled_eye_dy = scaled_eye_dy
    
    def calculate_pupil_shift(self, current_pupil_mm, baseline_pupil_mm):
        """
        计算瞳孔移动距离

        参数:
        - current_pupil_mm: 当前瞳孔大小 (mm)
        - baseline_pupil_mm: 基线瞳孔大小 (mm)

        返回:
        - pupil_shift_x: 瞳孔水平移动距离 (像素)
        """
        if baseline_pupil_mm is None or baseline_pupil_mm <= 0 or current_pupil_mm is None:
            return 0.0

        # 计算瞳孔差值
        pupil_diff = current_pupil_mm - baseline_pupil_mm

        # 应用移动公式: 移动像素 = k * 方向 * (当前瞳孔 - 基线瞳孔)
        pupil_shift_x = config.face_pupil_scale * config.face_pupil_direction * pupil_diff

        # 应用左右移动范围限制
        if pupil_shift_x > 0:  # 向右移动
            # 限制右移范围: 0 = 中心位置, 1 = 最大范围
            max_right_shift = self.max_pupil_shift * config.face_pupil_right_limit
            pupil_shift_x = min(pupil_shift_x, max_right_shift)
        else:  # 向左移动
            # 限制左移范围: 0 = 中心位置, 1 = 最大范围
            max_left_shift = -self.max_pupil_shift * config.face_pupil_left_limit
            pupil_shift_x = max(pupil_shift_x, max_left_shift)

        return pupil_shift_x
    
    def update_pupil_positions(self, pupil_shift_x):
        """
        更新瞳孔位置

        参数:
        - pupil_shift_x: 瞳孔水平移动距离 (像素)
        """
        # 更新左瞳孔位置，使用缩放后的位置参数
        left_pupil_x = -self.scaled_eye_dx + pupil_shift_x
        self.face_components['pupil_left'].pos = (left_pupil_x, self.scaled_eye_dy)

        # 更新右瞳孔位置，使用缩放后的位置参数
        right_pupil_x = self.scaled_eye_dx + pupil_shift_x
        self.face_components['pupil_right'].pos = (right_pupil_x, self.scaled_eye_dy)
    
    def set_face_color(self, color):
        """
        设置整个脸部的颜色

        参数:
        - color: 颜色值 [R, G, B]
        """
        for component_name, component in self.face_components.items():
            if hasattr(component, 'lineColor'):
                component.lineColor = color
            if hasattr(component, 'fillColor') and (component_name.startswith('pupil') or component_name == 'nose'):
                component.fillColor = color
    
    def draw_face(self):
        """绘制整个笑脸"""
        for component in self.face_components.values():
            component.draw()

# 显示函数接口，供主训练程序调用
def show_face_baseline(win, renderer, baseline_pupil_mm):
    """
    基线阶段的笑脸显示 - 显示静态笑脸，瞳孔在中心位置
    
    参数:
    - win: PsychoPy窗口
    - renderer: 笑脸渲染器
    - baseline_pupil_mm: 基线瞳孔大小
    """
    # 基线阶段瞳孔在中心位置，不移动
    renderer.update_pupil_positions(0.0)
    
    # 设置默认颜色
    renderer.set_face_color(config.face_default_color)
    
    # 绘制笑脸
    renderer.draw_face()
    
    return True

def show_face_modulation(win, renderer, current_pupil_mm, baseline_pupil_mm):
    """
    自主调适阶段的笑脸显示 - 瞳孔根据瞳孔大小差值左右移动
    
    参数:
    - win: PsychoPy窗口
    - renderer: 笑脸渲染器
    - current_pupil_mm: 当前瞳孔大小
    - baseline_pupil_mm: 基线瞳孔大小
    
    返回:
    - pupil_shift_x: 当前瞳孔移动距离
    """
    # 计算瞳孔移动距离
    pupil_shift_x = renderer.calculate_pupil_shift(current_pupil_mm, baseline_pupil_mm)
    
    # 更新瞳孔位置
    renderer.update_pupil_positions(pupil_shift_x)
    
    # 设置默认颜色
    renderer.set_face_color(config.face_default_color)
    
    # 绘制笑脸
    renderer.draw_face()
    
    return pupil_shift_x

def show_face_feedback(win, renderer, best_effect_pupil_mm, baseline_pupil_mm, success):
    """
    反馈阶段的笑脸显示 - 显示最佳效果时的瞳孔位置，用颜色表示成功/失败
    
    参数:
    - win: PsychoPy窗口
    - renderer: 笑脸渲染器
    - best_effect_pupil_mm: 最佳效果瞳孔大小
    - baseline_pupil_mm: 基线瞳孔大小
    - success: 是否成功
    
    返回:
    - pupil_shift_x: 最佳效果对应的瞳孔移动距离
    """
    # 计算最佳效果对应的瞳孔移动距离
    pupil_shift_x = renderer.calculate_pupil_shift(best_effect_pupil_mm, baseline_pupil_mm)
    
    # 更新瞳孔位置
    renderer.update_pupil_positions(pupil_shift_x)
    
    # 根据成功与否设置颜色
    if success:
        color = config.face_success_color
    else:
        color = config.face_failure_color
    
    renderer.set_face_color(color)
    
    # 绘制笑脸
    renderer.draw_face()
    
    return pupil_shift_x

def main():
    """
    主函数，用于测试face显示效果
    展示三个阶段的效果：基线、自主调节、反馈
    """
    from psychopy import visual, core, event, monitors

    # 设置窗口
    mon = monitors.Monitor('myMonitor', width=config.monitor_width, distance=config.monitor_distance)
    win = visual.Window(
        size=[1920, 1080],
        monitor=mon,
        winType='pyglet',
        units='height',  # 使用height单位以匹配face_code.txt
        color=config.bg_color,
        fullscr=True
    )

    # 创建笑脸渲染器
    renderer = FaceRenderer(win)

    # 创建文本显示当前状态
    status_text = visual.TextStim(
        win,
        text="",
        pos=(0, -0.6),
        height=0.03,
        color=[1, 1, 1],
        font=config.font_name
    )

    # 创建说明文本
    instruction_text = visual.TextStim(
        win,
        text="按键控制:\n1: 基线阶段\n2: 自主调节阶段\n3: 反馈阶段\n左右箭头: 调整瞳孔大小\nESC: 退出",
        pos=(0, 0.6),
        height=0.025,
        color=[1, 1, 1],
        font=config.font_name,
        wrapWidth=1.0
    )

    # 测试参数
    baseline_pupil = 4.0  # 基线瞳孔大小 (mm)
    current_pupil = 4.0   # 当前瞳孔大小 (mm)
    test_phase = 1        # 当前测试阶段 (1=基线, 2=自主调节, 3=反馈)
    success = True        # 反馈阶段的成功状态

    print("Face笑脸测试程序启动")
    print("使用数字键1-3切换阶段，左右箭头调整瞳孔大小，ESC退出")

    # 主循环
    while True:
        # 检查键盘输入
        keys = event.getKeys()
        for key in keys:
            if key == 'escape':
                win.close()
                core.quit()
                return
            elif key == '1':
                test_phase = 1
                print("切换到基线阶段")
            elif key == '2':
                test_phase = 2
                print("切换到自主调节阶段")
            elif key == '3':
                test_phase = 3
                print("切换到反馈阶段")
            elif key == 'left':
                current_pupil = max(1.0, current_pupil - 0.1)
                print(f"瞳孔大小: {current_pupil:.1f}mm")
            elif key == 'right':
                current_pupil = min(8.0, current_pupil + 0.1)
                print(f"瞳孔大小: {current_pupil:.1f}mm")
            elif key == 'space':
                success = not success
                print(f"反馈状态: {'成功' if success else '失败'}")

        # 清空屏幕
        win.clearBuffer()

        # 根据当前阶段显示相应内容
        if test_phase == 1:
            # 基线阶段
            show_face_baseline(win, renderer, baseline_pupil)
            pupil_shift = 0.0
            status_text.text = f"基线阶段 | 基线瞳孔: {baseline_pupil:.1f}mm | 瞳孔移动: {pupil_shift:.1f}像素"
        elif test_phase == 2:
            # 自主调节阶段
            pupil_shift = show_face_modulation(win, renderer, current_pupil, baseline_pupil)
            pupil_diff = current_pupil - baseline_pupil
            status_text.text = f"自主调节阶段 | 当前瞳孔: {current_pupil:.1f}mm | 瞳孔差值: {pupil_diff:+.1f}mm | 瞳孔移动: {pupil_shift:.1f}像素"
        elif test_phase == 3:
            # 反馈阶段
            pupil_shift = show_face_feedback(win, renderer, current_pupil, baseline_pupil, success)
            pupil_diff = current_pupil - baseline_pupil
            status_text.text = f"反馈阶段 | 最佳瞳孔: {current_pupil:.1f}mm | 瞳孔差值: {pupil_diff:+.1f}mm | 瞳孔移动: {pupil_shift:.1f}像素 | 状态: {'成功' if success else '失败'}"

        # 绘制文本
        instruction_text.draw()
        status_text.draw()

        # 刷新屏幕
        win.flip()

        # 控制帧率
        core.wait(1.0 / 60)  # 60 FPS

if __name__ == '__main__':
    main()
