# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练程序
基于PsychoPy和EyeLink眼动仪
实现实时瞳孔大小反馈训练

作者: AI Assistant
日期: 2025-07-09
"""

from __future__ import division
from __future__ import print_function

import pylink
import os
import platform
import random
import time
import sys
import numpy as np
from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy
from psychopy import visual, core, event, monitors, gui
from psychopy.hardware import keyboard
from math import fabs, pi
from string import ascii_letters, digits

# 切换到脚本文件夹
script_path = os.path.dirname(sys.argv[0])
if len(script_path) != 0:
    os.chdir(script_path)

# 只显示关键日志信息
from psychopy import logging
logging.console.setLevel(logging.CRITICAL)

# 导入配置文件
from config import config

# 导入3D小人显示模块
if config.display_mode == "3d_walk":
    import walk_3d
    from walk_3d import Walk3<PERSON><PERSON><PERSON>, show_3d_baseline, show_3d_modulation, show_3d_feedback

# 导入Face笑脸显示模块
if config.display_mode == "face":
    import face
    from face import FaceRenderer, show_face_baseline, show_face_modulation, show_face_feedback

def get_experiment_info():
    """获取实验信息"""
    # 获取EDF文件名
    dlg_title = "实验设置"
    dlg_prompt = "请输入EDF文件名 (8个字符以内，只能包含字母、数字和下划线)"
    
    while True:
        dlg = gui.Dlg(dlg_title)
        dlg.addText(dlg_prompt)
        dlg.addField("EDF文件名:", "BIOFDBK")
        dlg.addField("训练顺序:", choices=["先放大后缩小", "先缩小后放大"])
        dlg.addField("被试编号:", "001")
        
        ok_data = dlg.show()
        if dlg.OK:
            edf_fname = ok_data["EDF文件名:"].rstrip().split(".")[0]
            training_order = ok_data["训练顺序:"]
            subject_id = ok_data["被试编号:"]
            
            # 检查文件名有效性
            allowed_char = ascii_letters + digits + '_'
            if not all([c in allowed_char for c in edf_fname]):
                print('错误: EDF文件名包含无效字符')
                continue
            elif len(edf_fname) > 8:
                print('错误: EDF文件名不能超过8个字符')
                continue
            else:
                break
        else:
            print("用户取消")
            core.quit()
            sys.exit()
    
    return edf_fname, training_order, subject_id

def setup_eyelink(edf_fname):
    """设置EyeLink连接和配置"""
    # 连接EyeLink
    if config.dummy_mode:
        el_tracker = pylink.EyeLink(None)
    else:
        try:
            el_tracker = pylink.EyeLink(config.eyelink_ip)
        except RuntimeError as error:
            print('错误:', error)
            core.quit()
            sys.exit()
    
    # 打开EDF数据文件
    edf_file = edf_fname + ".EDF"
    try:
        el_tracker.openDataFile(edf_file)
    except RuntimeError as err:
        print('错误:', err)
        if el_tracker.isConnected():
            el_tracker.close()
        core.quit()
        sys.exit()
    
    # 添加文件头信息
    preamble_text = 'RECORDED BY %s' % os.path.basename(__file__)
    el_tracker.sendCommand("add_file_preamble_text '%s'" % preamble_text)
    
    # 配置追踪器
    el_tracker.setOfflineMode()
    
    # 获取EyeLink版本
    eyelink_ver = 6
    if not config.dummy_mode:
        vstr = el_tracker.getTrackerVersionString()
        eyelink_ver = int(vstr.split()[-1].split('.')[0])
        print('在 %s 版本 %d 上运行实验' % (vstr, eyelink_ver))
    
    return el_tracker, edf_file, eyelink_ver

def configure_eyelink(el_tracker, eyelink_ver, scn_width, scn_height):
    """配置EyeLink参数"""
    # 文件和链路数据控制
    file_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT'
    link_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE,INPUT'
    
    # 包含瞳孔数据
    if eyelink_ver > 3:
        file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,HTARGET,GAZERES,BUTTON,STATUS,INPUT'
        link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,HTARGET,STATUS,INPUT'
    else:
        file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,GAZERES,BUTTON,STATUS,INPUT'
        link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS,INPUT'
    
    el_tracker.sendCommand("file_event_filter = %s" % file_event_flags)
    el_tracker.sendCommand("file_sample_data = %s" % file_sample_flags)
    el_tracker.sendCommand("link_event_filter = %s" % link_event_flags)
    el_tracker.sendCommand("link_sample_data = %s" % link_sample_flags)
    
    # 设置瞳孔大小为直径模式
    el_tracker.setPupilSizeDiameter("YES")
    
    # 设置采样率
    if eyelink_ver > 2:
        el_tracker.sendCommand("sample_rate %d" % config.sampling_rate)
    
    # 设置校准类型
    el_tracker.sendCommand("calibration_type = %s" % config.calibration_type)
    
    # 设置屏幕坐标
    el_coords = "screen_pixel_coords = 0 0 %d %d" % (scn_width - 1, scn_height - 1)
    el_tracker.sendCommand(el_coords)
    
    # 发送显示坐标信息给Data Viewer
    dv_coords = "DISPLAY_COORDS  0 0 %d %d" % (scn_width - 1, scn_height - 1)
    el_tracker.sendMessage(dv_coords)

def setup_psychopy_window():
    """设置PsychoPy窗口"""
    mon = monitors.Monitor('myMonitor', width=config.monitor_width, distance=config.monitor_distance)
    win = visual.Window(fullscr=config.full_screen,
                        monitor=mon,
                        winType='pyglet',
                        units='pix',
                        screen = 1,
                        color=config.bg_color)
    
    # 获取屏幕分辨率
    scn_width, scn_height = win.size
    
    # macOS Retina显示器分辨率修正
    if 'Darwin' in platform.system():
        # 这里可以添加Retina检测逻辑
        pass
    
    return win, scn_width, scn_height

def setup_graphics_environment(el_tracker, win):
    """设置图形环境"""
    genv = EyeLinkCoreGraphicsPsychoPy(el_tracker, win)
    print(genv)  # 打印CoreGraphics库版本
    
    # 设置校准颜色
    foreground_color = (-1, -1, -1)  # 黑色
    background_color = win.color
    genv.setCalibrationColors(foreground_color, background_color)
    
    # 设置校准目标
    genv.setTargetType('circle')
    genv.setTargetSize(24)
    genv.setCalibrationSounds('', '', '')
    
    # 请求Pylink使用PsychoPy窗口进行校准
    pylink.openGraphicsEx(genv)
    
    return genv

# 辅助函数
def clear_screen(win, genv):
    """清空屏幕"""
    win.fillColor = genv.getBackgroundColor()
    win.flip()

def show_message(win, genv, text, duration=None, wait_for_key=False):
    """显示消息"""
    msg = visual.TextStim(win, text,
                          color=genv.getForegroundColor(),
                          wrapWidth=config.text_wrap_width,
                          font=config.font_name,
                          height=config.font_size)
    clear_screen(win, genv)
    msg.draw()
    win.flip()

    if wait_for_key:
        event.waitKeys()
        clear_screen(win, genv)
    elif duration:
        core.wait(duration)
        clear_screen(win, genv)

def terminate_task(win, genv, edf_file, session_folder, session_identifier):
    """优雅地终止任务并下载EDF文件"""
    el_tracker = pylink.getEYELINK()

    if el_tracker.isConnected():
        # 如果正在记录，先终止当前试次
        error = el_tracker.isRecording()
        if error == pylink.TRIAL_OK:
            abort_trial(win, genv)

        # 设置为离线模式
        el_tracker.setOfflineMode()

        # 清空Host PC屏幕并等待
        el_tracker.sendCommand('clear_screen 0')
        pylink.msecDelay(500)

        # 关闭EDF数据文件
        el_tracker.closeDataFile()

        # 显示文件传输消息
        msg = 'EDF数据正在从EyeLink Host PC传输...'
        show_message(win, genv, msg, wait_for_key=False)

        # 下载EDF文件到本地
        local_edf = os.path.join(session_folder, session_identifier + '.EDF')
        try:
            el_tracker.receiveDataFile(edf_file, local_edf)
        except RuntimeError as error:
            print('错误:', error)

        # 关闭与追踪器的连接
        el_tracker.close()

    # 关闭PsychoPy窗口
    win.close()

    # 退出PsychoPy
    core.quit()
    sys.exit()

def abort_trial(win, genv):
    """终止试次"""
    el_tracker = pylink.getEYELINK()

    # 停止记录
    if el_tracker.isRecording():
        # 添加100ms来捕获最终试次事件
        pylink.pumpDelay(100)
        el_tracker.stopRecording()

    # 清空屏幕
    clear_screen(win, genv)

    # 发送消息清空Data Viewer屏幕
    bgcolor_RGB = (116, 116, 116)
    el_tracker.sendMessage('!V CLEAR %d %d %d' % bgcolor_RGB)

    # 发送试次结束消息
    el_tracker.sendMessage('TRIAL_RESULT %d' % pylink.TRIAL_ERROR)

class PupilDataProcessor:
    """瞳孔数据处理器"""
    def __init__(self):
        self.pupil_buffer = []  # 瞳孔数据缓冲区
        self.baseline_pupil_size = None  # 基线瞳孔大小 (mm)
        self.last_valid_pupil_size = None  # 上一个有效瞳孔大小 (mm)
        self.last_update_time = None  # 上次更新时间

    def pixels_to_mm(self, pupil_pixels):
        """将瞳孔像素值转换为毫米"""
        if pupil_pixels <= 0:
            return 0
        # 使用校准参数进行转换
        conversion_factor = config.pupil_calibration_mm / config.pupil_calibration_pixels
        return pupil_pixels * conversion_factor

    def mm_to_pixels(self, pupil_mm):
        """将瞳孔毫米值转换为像素"""
        if pupil_mm <= 0:
            return 0
        # 使用校准参数进行转换
        conversion_factor = config.pupil_calibration_pixels / config.pupil_calibration_mm
        return pupil_mm * conversion_factor

    def add_sample(self, pupil_size_pixels, timestamp):
        """添加瞳孔样本（输入为像素值）"""
        # 将像素值转换为毫米
        pupil_size_mm = self.pixels_to_mm(pupil_size_pixels)

        # 检查瞳孔大小是否在有效范围内（毫米）
        if config.pupil_min_size_mm <= pupil_size_mm <= config.pupil_max_size_mm:
            # 检查变化速率
            if self.last_valid_pupil_size is not None and self.last_update_time is not None:
                time_diff = timestamp - self.last_update_time  # 时间差 (ms)
                if time_diff > 0:
                    change_rate = abs(pupil_size_mm - self.last_valid_pupil_size) / time_diff
                    if change_rate > config.max_change_rate_mm:
                        print(f"瞳孔变化速率过快: {change_rate:.6f} mm/ms > {config.max_change_rate_mm} mm/ms")
                        return False  # 变化速率过快，拒绝此样本

            # 添加到缓冲区（存储毫米值）
            self.pupil_buffer.append((pupil_size_mm, timestamp))
            self.last_valid_pupil_size = pupil_size_mm
            self.last_update_time = timestamp

            # 保持缓冲区大小
            if len(self.pupil_buffer) > config.samples_per_feedback:
                self.pupil_buffer.pop(0)

            return True
        else:
            print(f"瞳孔大小超出范围: {pupil_size_mm:.3f}mm (范围: {config.pupil_min_size_mm}-{config.pupil_max_size_mm}mm)")
            return False

    def get_averaged_pupil_size(self):
        """获取平均瞳孔大小"""
        if len(self.pupil_buffer) >= config.samples_per_feedback:
            recent_samples = self.pupil_buffer[-config.samples_per_feedback:]
            return np.mean([sample[0] for sample in recent_samples])
        elif len(self.pupil_buffer) > 0:
            return np.mean([sample[0] for sample in self.pupil_buffer])
        else:
            return self.last_valid_pupil_size

    def set_baseline(self, baseline_duration_samples=None):
        """设置基线瞳孔大小"""
        if baseline_duration_samples is None:
            baseline_duration_samples = int(config.baseline_duration * 1000 / 33)  # 最后几秒的样本

        if len(self.pupil_buffer) >= baseline_duration_samples:
            baseline_samples = self.pupil_buffer[-baseline_duration_samples:]
            self.baseline_pupil_size = np.mean([sample[0] for sample in baseline_samples])
        elif len(self.pupil_buffer) > 0:
            self.baseline_pupil_size = np.mean([sample[0] for sample in self.pupil_buffer])

        return self.baseline_pupil_size

    def clear_buffer(self):
        """清空缓冲区"""
        self.pupil_buffer = []
        self.last_valid_pupil_size = None
        self.last_update_time = None

def create_visual_stimuli(win):
    """创建视觉刺激"""
    stimuli = {}

    # 注视点
    stimuli['fixation'] = visual.TextStim(win, '+',
                                         height=50,
                                         color=config.fixation_color,
                                         pos=(0, 0))

    # 根据显示模式创建不同的刺激
    if config.display_mode == "circle" or config.debug_mode:
        # 基线虚线圆圈
        stimuli['baseline_circle'] = visual.Circle(win,
                                                  radius=config.baseline_circle_radius,
                                                  lineWidth=config.circle_line_width,
                                                  lineColor=config.baseline_color,
                                                  fillColor=None,
                                                  pos=(0, 0))
        # 设置为虚线样式（如果PsychoPy支持）
        try:
            stimuli['baseline_circle'].lineStyle = '--'  # 虚线样式
        except:
            pass  # 如果不支持虚线，就使用实线

        # 实时反馈圆圈
        stimuli['feedback_circle'] = visual.Circle(win,
                                                  radius=config.baseline_circle_radius,
                                                  lineWidth=config.circle_line_width,
                                                  lineColor=config.feedback_color,
                                                  fillColor=None,
                                                  pos=(0, 0))

    if config.display_mode == "3d_walk":
        # 创建3D小人渲染器
        stimuli['walk_3d_renderer'] = Walk3DRenderer(win)
        print("3D小人渲染器已创建")

    if config.display_mode == "face":
        # 创建Face笑脸渲染器
        stimuli['face_renderer'] = FaceRenderer(win)
        print("Face笑脸渲染器已创建")
        
    # 在debug模式下创建情绪等级显示文本
    if config.debug_mode:
        # 计算屏幕右侧位置
        screen_width = win.size[0]
        screen_height = win.size[1]
        right_x = screen_width // 2 - 150  # 右侧边缘向内150像素
        
        # 显示模式信息文本（根据不同模式显示不同内容）
        stimuli['emotion_level_text'] = visual.TextStim(
            win,
            text="显示模式信息",
            pos=(right_x, 200),
            height=24,
            color=[1, 1, 1],  # 白色文本
            font=config.font_name,
            anchorHoriz='center',
            anchorVert='center'
        )
        
        # 瞳孔大小显示文本
        stimuli['pupil_size_text'] = visual.TextStim(
            win,
            text="瞳孔大小: --",
            pos=(right_x, 150),
            height=20,
            color=[1, 1, 1],  # 白色文本
            font=config.font_name,
            anchorHoriz='center',
            anchorVert='center'
        )
        
        # 基线瞳孔大小显示文本
        stimuli['baseline_pupil_text'] = visual.TextStim(
            win,
            text="基线瞳孔: --",
            pos=(right_x, 100),
            height=20,
            color=[0.8, 0.8, 0.8],  # 灰色文本
            font=config.font_name,
            anchorHoriz='center',
            anchorVert='center'
        )
        
        # 瞳孔差值显示文本
        stimuli['pupil_diff_text'] = visual.TextStim(
            win,
            text="瞳孔差值: --",
            pos=(right_x, 50),
            height=20,
            color=[0.6, 0.6, 1.0],  # 淡蓝色文本
            font=config.font_name,
            anchorHoriz='center',
            anchorVert='center'
        )
        
        print("3D模式debug信息显示已创建")

    return stimuli

def calculate_circle_properties(pupil_size_mm, baseline_size_mm):
    """
    计算圆圈属性，按照新的显示逻辑
    公式: k*(real_pupil-base_pupil) + base_circle = real_circle

    参数:
    - pupil_size_mm: 当前瞳孔大小 (mm)
    - baseline_size_mm: 基线瞳孔大小 (mm)

    返回:
    - new_radius: 新的圆圈半径 (像素)
    - new_line_width: 新的线宽 (保持总像素数恒定)
    """
    if baseline_size_mm is None or baseline_size_mm <= 0:
        return config.baseline_circle_radius, config.circle_line_width

    # 使用新的公式计算半径
    # k*(real_pupil-base_pupil) + base_circle = real_circle
    pupil_diff = pupil_size_mm - baseline_size_mm  # 瞳孔大小差值 (mm)
    new_radius = config.pupil_display_scale * pupil_diff + config.baseline_circle_radius

    # 确保半径不为负数
    new_radius = max(new_radius, 10)  # 最小半径10像素

    # 计算线宽以保持总像素数恒定
    # 圆圈的像素数约等于 2 * π * radius * lineWidth
    # 保持 radius * lineWidth 恒定
    reference_pixels = config.baseline_circle_radius * config.circle_line_width
    new_line_width = reference_pixels / new_radius

    # 限制线宽范围
    new_line_width = max(0.5, min(new_line_width, 50))

    return new_radius, new_line_width

def get_baseline_circle_properties():
    """
    获取基线圆圈属性（固定大小）

    返回:
    - radius: 基线圆圈半径 (像素)
    - line_width: 基线圆圈线宽
    """
    return config.baseline_circle_radius, config.circle_line_width

def show_phase_prompt(win, genv, prompt_text):
    """显示阶段提示语"""
    if config.show_phase_prompts:
        show_message(win, genv, prompt_text, duration=config.phase_prompt_duration)

def run_baseline_phase(el_tracker, win, genv, stimuli, pupil_processor, kb):
    """运行基线阶段"""
    # 显示阶段提示语
    show_phase_prompt(win, genv, config.baseline_prompt)

    el_tracker.sendMessage('PHASE_START BASELINE')

    el_tracker.sendMessage('baseline_onset')

    # 发送Data Viewer消息
    scn_width, scn_height = win.size
    h_center = int(scn_width/2.0)
    v_center = int(scn_height/2.0)
    el_tracker.sendMessage('!V CLEAR 128 128 128')  # 清空屏幕

    start_time = core.getTime()
    last_display_time = start_time

    # 确定使用哪只眼睛
    eye_used = el_tracker.eyeAvailable()
    if eye_used == 1:
        eye_used = 1
    elif eye_used == 0 or eye_used == 2:
        eye_used = 0
    else:
        print("获取眼睛信息错误!")
        return pylink.TRIAL_ERROR

    print(f"基线阶段开始，显示模式: {config.display_mode}")

    # 收集基线数据
    while core.getTime() - start_time < config.baseline_duration:
        current_time = core.getTime()

        # 检查是否还在记录
        error = el_tracker.isRecording()
        if error is not pylink.TRIAL_OK:
            el_tracker.sendMessage('tracker_disconnected')
            abort_trial(win, genv)
            return error

        # 获取最新样本
        sample = el_tracker.getNewestSample()
        if sample is not None:
            # 获取瞳孔大小
            if eye_used == 1 and sample.isRightSample():
                pupil_size = sample.getRightEye().getPupilSize()
            elif eye_used == 0 and sample.isLeftSample():
                pupil_size = sample.getLeftEye().getPupilSize()
            else:
                continue

            # 添加到处理器
            if pupil_size > 0:  # 确保瞳孔大小有效
                pupil_processor.add_sample(pupil_size, sample.getTime())

        # 每隔一定时间更新显示（约30Hz）
        if current_time - last_display_time >= 1.0/30:
            last_display_time = current_time

            # 根据显示模式更新显示
            if config.display_mode == "circle" or config.debug_mode:
                # 显示注视点和基线圆圈
                stimuli['fixation'].draw()
                stimuli['baseline_circle'].draw()
            if config.display_mode == "3d_walk":
                # 显示注视点和3D小人动态动画
                stimuli['fixation'].draw()
                show_3d_baseline(win, stimuli['walk_3d_renderer'], None)
            if config.display_mode == "face":
                # 显示注视点和笑脸
                stimuli['fixation'].draw()
                show_face_baseline(win, stimuli['face_renderer'], None)
            
            # 在debug模式下显示基线阶段信息
            if config.debug_mode:
                stimuli['emotion_level_text'].text = "情绪等级: 0 (基线)"
                stimuli['pupil_size_text'].text = "瞳孔大小: 收集中..."
                stimuli['baseline_pupil_text'].text = "基线瞳孔: 计算中..."
                stimuli['pupil_diff_text'].text = "瞳孔差值: --"

                # 绘制debug信息
                stimuli['emotion_level_text'].draw()
                stimuli['pupil_size_text'].draw()
                stimuli['baseline_pupil_text'].draw()
                stimuli['pupil_diff_text'].draw()

            win.flip()

        # 检查键盘事件
        keyPressList = kb.getKeys(keyList=None, waitRelease=False, clear=False)
        if len(keyPressList) > 0:
            keyPressNamesList = [keyPress.name for keyPress in keyPressList]

            # 按ESC键跳过试次
            if 'escape' in keyPressNamesList:
                el_tracker.sendMessage('trial_skipped_by_user')
                clear_screen(win, genv)
                abort_trial(win, genv)
                return pylink.SKIP_TRIAL

            # 按Ctrl-C终止任务
            if 'c' in keyPressNamesList and ('lctrl' in keyPressNamesList or 'rctrl' in keyPressNamesList):
                el_tracker.sendMessage('terminated_by_user')
                terminate_task(win, genv, None, None, None)
                return pylink.ABORT_EXPT

    # 设置基线
    baseline_size = pupil_processor.set_baseline()
    if baseline_size is not None:
        el_tracker.sendMessage('BASELINE_SIZE %.3f' % baseline_size)
    el_tracker.sendMessage('PHASE_END BASELINE')

    return baseline_size

def run_modulation_phase(el_tracker, win, genv, stimuli, pupil_processor, condition, baseline_size, kb, eye_used):
    """运行自主调适阶段"""
    # 显示阶段提示语
    if condition == "enlarge":
        prompt_text = config.modulation_prompt_enlarge
    else:
        prompt_text = config.modulation_prompt_shrink
    show_phase_prompt(win, genv, prompt_text)

    el_tracker.sendMessage('PHASE_START MODULATION')
    el_tracker.sendMessage('CONDITION %s' % condition)
    el_tracker.sendMessage('modulation_onset')

    start_time = core.getTime()
    last_feedback_time = start_time
    last_display_time = start_time

    modulation_data = []  # 存储调节期间的瞳孔数据

    # 只在圆圈模式下设置基线圆圈
    if config.display_mode == "circle" or config.debug_mode:
        baseline_radius, baseline_line_width = get_baseline_circle_properties()
        stimuli['baseline_circle'].radius = baseline_radius
        stimuli['baseline_circle'].lineWidth = baseline_line_width
        print(f"自主调适阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, 固定基线圆圈半径: {baseline_radius}像素")
    if config.display_mode == "3d_walk":
        print(f"自主调适阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, 3D小人模式")
    if config.display_mode == "face":
        print(f"自主调适阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, Face笑脸模式")

    # 初始显示基本界面
    emotion_level = 0  # 初始化emotion_level变量
    pupil_shift = 0.0  # 初始化pupil_shift变量

    if config.display_mode == "circle" or config.debug_mode:
        stimuli['fixation'].draw()
        stimuli['baseline_circle'].draw()
        stimuli['feedback_circle'].draw()
    if config.display_mode == "3d_walk":
        stimuli['fixation'].draw()
        emotion_level = show_3d_modulation(win, stimuli['walk_3d_renderer'], None, baseline_size)
    if config.display_mode == "face":
        stimuli['fixation'].draw()
        pupil_shift = show_face_modulation(win, stimuli['face_renderer'], None, baseline_size)

    # 在debug模式下显示初始信息
    if config.debug_mode:
        # 根据显示模式显示不同的信息
        if config.display_mode == "3d_walk":
            stimuli['emotion_level_text'].text = f"情绪等级: {emotion_level}"
        elif config.display_mode == "face":
            stimuli['emotion_level_text'].text = f"瞳孔移动: {pupil_shift:.1f}像素"
        else:
            stimuli['emotion_level_text'].text = "圆圈模式"

        stimuli['pupil_size_text'].text = "瞳孔大小: --"
        stimuli['baseline_pupil_text'].text = f"基线瞳孔: {baseline_size:.3f}mm" if baseline_size is not None else "基线瞳孔: --"
        stimuli['pupil_diff_text'].text = "瞳孔差值: --"

        # 绘制debug信息
        stimuli['emotion_level_text'].draw()
        stimuli['pupil_size_text'].draw()
        stimuli['baseline_pupil_text'].draw()
        stimuli['pupil_diff_text'].draw()
        
    win.flip()

    while core.getTime() - start_time < config.modulation_duration:
        current_time = core.getTime()

        # 检查是否还在记录
        error = el_tracker.isRecording()
        if error is not pylink.TRIAL_OK:
            el_tracker.sendMessage('tracker_disconnected')
            abort_trial(win, genv)
            return error, []

        # 获取最新样本
        sample = el_tracker.getNewestSample()
        if sample is not None:
            # 获取瞳孔大小
            if eye_used == 1 and sample.isRightSample():
                pupil_size = sample.getRightEye().getPupilSize()
            elif eye_used == 0 and sample.isLeftSample():
                pupil_size = sample.getLeftEye().getPupilSize()
            else:
                continue

            # 添加到处理器
            if pupil_size > 0:
                if pupil_processor.add_sample(pupil_size, sample.getTime()):
                    # 存储转换后的毫米值，而不是原始像素值
                    pupil_size_mm = pupil_processor.pixels_to_mm(pupil_size)
                    modulation_data.append(pupil_size_mm)

        # 每32ms更新一次反馈 (约30Hz)
        if current_time - last_feedback_time >= 1.0/config.feedback_rate:
            last_feedback_time = current_time

            # 获取当前平均瞳孔大小
            current_pupil_size = pupil_processor.get_averaged_pupil_size()

            if current_pupil_size is not None and baseline_size is not None:
                # 记录反馈数据
                el_tracker.sendMessage('FEEDBACK_PUPIL %.3f' % current_pupil_size)

                if config.display_mode == "circle" or config.debug_mode:
                    # 计算实时反馈圆圈属性
                    # 使用新公式: k*(real_pupil-base_pupil) + base_circle = real_circle
                    new_radius, new_line_width = calculate_circle_properties(
                        current_pupil_size, baseline_size)

                    # 更新反馈圆圈
                    stimuli['feedback_circle'].radius = new_radius
                    stimuli['feedback_circle'].lineWidth = new_line_width

            # 无论是否有瞳孔数据，都要绘制界面
            if config.display_mode == "circle" or config.debug_mode:
                stimuli['fixation'].draw()
                stimuli['baseline_circle'].draw()
                stimuli['feedback_circle'].draw()
            if config.display_mode == "3d_walk":
                stimuli['fixation'].draw()
                emotion_level = show_3d_modulation(win, stimuli['walk_3d_renderer'], current_pupil_size, baseline_size)
            if config.display_mode == "face":
                stimuli['fixation'].draw()
                pupil_shift = show_face_modulation(win, stimuli['face_renderer'], current_pupil_size, baseline_size)

            # 在debug模式下显示实时信息
            if config.debug_mode:
                # 根据显示模式更新不同的信息
                if config.display_mode == "3d_walk":
                    stimuli['emotion_level_text'].text = f"情绪等级: {emotion_level}"
                elif config.display_mode == "face":
                    stimuli['emotion_level_text'].text = f"瞳孔移动: {pupil_shift:.1f}像素"
                else:
                    stimuli['emotion_level_text'].text = "圆圈模式"
                
                # 更新瞳孔大小显示
                if current_pupil_size is not None:
                    stimuli['pupil_size_text'].text = f"瞳孔大小: {current_pupil_size:.3f}mm"
                else:
                    stimuli['pupil_size_text'].text = "瞳孔大小: --"
                
                # 更新基线瞳孔显示
                if baseline_size is not None:
                    stimuli['baseline_pupil_text'].text = f"基线瞳孔: {baseline_size:.3f}mm"
                else:
                    stimuli['baseline_pupil_text'].text = "基线瞳孔: --"
                
                # 更新瞳孔差值显示
                if current_pupil_size is not None and baseline_size is not None:
                    pupil_diff = current_pupil_size - baseline_size
                    stimuli['pupil_diff_text'].text = f"瞳孔差值: {pupil_diff:+.3f}mm"
                else:
                    stimuli['pupil_diff_text'].text = "瞳孔差值: --"
                
                # 绘制debug信息
                stimuli['emotion_level_text'].draw()
                stimuli['pupil_size_text'].draw()
                stimuli['baseline_pupil_text'].draw()
                stimuli['pupil_diff_text'].draw()
                
            win.flip()

        # 确保至少每100ms更新一次显示（即使没有到反馈时间）
        elif current_time - last_display_time >= 0.1:
            last_display_time = current_time
            if config.display_mode == "circle" or config.debug_mode:
                stimuli['fixation'].draw()
                stimuli['baseline_circle'].draw()
                stimuli['feedback_circle'].draw()
            if config.display_mode == "3d_walk":
                stimuli['fixation'].draw()
                current_pupil_size = pupil_processor.get_averaged_pupil_size()
                emotion_level = show_3d_modulation(win, stimuli['walk_3d_renderer'], current_pupil_size, baseline_size)
            if config.display_mode == "face":
                stimuli['fixation'].draw()
                current_pupil_size = pupil_processor.get_averaged_pupil_size()
                pupil_shift = show_face_modulation(win, stimuli['face_renderer'], current_pupil_size, baseline_size)

            # 在debug模式下显示实时信息
            if config.debug_mode:
                # 根据显示模式更新不同的信息
                if config.display_mode == "3d_walk":
                    stimuli['emotion_level_text'].text = f"情绪等级: {emotion_level}"
                elif config.display_mode == "face":
                    stimuli['emotion_level_text'].text = f"瞳孔移动: {pupil_shift:.1f}像素"
                else:
                    stimuli['emotion_level_text'].text = "圆圈模式"
                
                # 更新瞳孔大小显示
                if current_pupil_size is not None:
                    stimuli['pupil_size_text'].text = f"瞳孔大小: {current_pupil_size:.3f}mm"
                else:
                    stimuli['pupil_size_text'].text = "瞳孔大小: --"
                
                # 更新基线瞳孔显示
                if baseline_size is not None:
                    stimuli['baseline_pupil_text'].text = f"基线瞳孔: {baseline_size:.3f}mm"
                else:
                    stimuli['baseline_pupil_text'].text = "基线瞳孔: --"
                
                # 更新瞳孔差值显示
                if current_pupil_size is not None and baseline_size is not None:
                    pupil_diff = current_pupil_size - baseline_size
                    stimuli['pupil_diff_text'].text = f"瞳孔差值: {pupil_diff:+.3f}mm"
                else:
                    stimuli['pupil_diff_text'].text = "瞳孔差值: --"
                
                # 绘制debug信息
                stimuli['emotion_level_text'].draw()
                stimuli['pupil_size_text'].draw()
                stimuli['baseline_pupil_text'].draw()
                stimuli['pupil_diff_text'].draw()
            win.flip()

        # 检查键盘事件
        keyPressList = kb.getKeys(keyList=None, waitRelease=False, clear=False)
        if len(keyPressList) > 0:
            keyPressNamesList = [keyPress.name for keyPress in keyPressList]

            # 按ESC键跳过试次
            if 'escape' in keyPressNamesList:
                el_tracker.sendMessage('trial_skipped_by_user')
                clear_screen(win, genv)
                abort_trial(win, genv)
                return pylink.SKIP_TRIAL, []

            # 按Ctrl-C终止任务
            if 'c' in keyPressNamesList and ('lctrl' in keyPressNamesList or 'rctrl' in keyPressNamesList):
                el_tracker.sendMessage('terminated_by_user')
                terminate_task(win, genv, None, None, None)
                return pylink.ABORT_EXPT, []

    el_tracker.sendMessage('PHASE_END MODULATION')
    return pylink.TRIAL_OK, modulation_data

def run_feedback_phase(el_tracker, win, genv, stimuli, baseline_size, modulation_data, condition, kb):
    """运行反馈阶段"""
    # 显示阶段提示语
    show_phase_prompt(win, genv, config.feedback_prompt)

    el_tracker.sendMessage('PHASE_START FEEDBACK')
    el_tracker.sendMessage('feedback_onset')

    # 分析调节数据
    success = False
    feedback_value = baseline_size  # 默认使用基线值

    if len(modulation_data) > 0 and baseline_size is not None:
        if condition == "enlarge":
            # 放大条件：寻找最大值
            max_value = max(modulation_data)
            if max_value > baseline_size:
                success = True
                feedback_value = max_value
            else:
                feedback_value = max_value
        else:  # shrink
            # 缩小条件：寻找最小值
            min_value = min(modulation_data)
            if min_value < baseline_size:
                success = True
                feedback_value = min_value
            else:
                feedback_value = min_value
    else:
        # 没有调节数据时，使用基线值
        print("警告：没有调节数据，使用基线值作为反馈值")

    # 设置反馈圆圈颜色
    if success:
        feedback_color = config.success_color
        el_tracker.sendMessage('FEEDBACK_RESULT SUCCESS')
    else:
        feedback_color = config.failure_color
        el_tracker.sendMessage('FEEDBACK_RESULT FAILURE')

    # 只在圆圈模式下设置基线圆圈
    if config.display_mode == "circle" or config.debug_mode:
        baseline_radius, baseline_line_width = get_baseline_circle_properties()
        stimuli['baseline_circle'].radius = baseline_radius
        stimuli['baseline_circle'].lineWidth = baseline_line_width
        print(f"反馈阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, 固定基线圆圈半径: {baseline_radius}像素")
    elif config.display_mode == "3d_walk":
        print(f"反馈阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, 3D小人模式")
    elif config.display_mode == "face":
        print(f"反馈阶段 - 基线瞳孔大小: {baseline_size:.3f}mm, Face笑脸模式")

    # 根据显示模式显示反馈
    if config.display_mode == "circle" or config.debug_mode:
        # 计算最佳效果圆圈大小
        if feedback_value is not None and baseline_size is not None:
            # 使用新公式计算最佳效果圆圈
            best_radius, best_line_width = calculate_circle_properties(feedback_value, baseline_size)
            el_tracker.sendMessage('FEEDBACK_VALUE %.3f' % feedback_value)
            print(f"反馈阶段 - 最佳效果: {feedback_value:.3f}mm, 效果圆圈半径: {best_radius:.1f}像素")
        else:
            best_radius = config.baseline_circle_radius
            best_line_width = config.circle_line_width

        # 创建最佳效果圆圈（使用计算出的线宽保持总像素数恒定）
        best_effect_circle = visual.Circle(win,
                                          radius=best_radius,
                                          lineWidth=best_line_width,
                                          lineColor=feedback_color,
                                          fillColor=None,
                                          pos=(0, 0))

        # 显示反馈：基线圆圈（蓝色虚线）+ 最佳效果圆圈（绿色/红色实线）
        stimuli['fixation'].draw()
        stimuli['baseline_circle'].draw()  # 基线圆圈（蓝色虚线）
        best_effect_circle.draw()          # 最佳效果圆圈（绿色/红色实线）

    if config.display_mode == "3d_walk":
        # 重置3D小人反馈动画时间
        if hasattr(stimuli['walk_3d_renderer'], 'feedback_animation_start_time'):
            delattr(stimuli['walk_3d_renderer'], 'feedback_animation_start_time')

        el_tracker.sendMessage('FEEDBACK_VALUE %.3f' % (feedback_value if feedback_value else 0))
        print(f"反馈阶段 - 最佳效果: {feedback_value:.3f}mm, 3D小人动态情绪等级动画")

    if config.display_mode == "face":
        el_tracker.sendMessage('FEEDBACK_VALUE %.3f' % (feedback_value if feedback_value else 0))
        print(f"反馈阶段 - 最佳效果: {feedback_value:.3f}mm, Face笑脸瞳孔移动反馈")

    # 初始显示
    emotion_level = 0  # 初始化emotion_level变量
    pupil_shift = 0.0  # 初始化pupil_shift变量

    if config.display_mode == "circle" or config.debug_mode:
        stimuli['fixation'].draw()
        stimuli['baseline_circle'].draw()  # 基线圆圈（蓝色虚线）
        best_effect_circle.draw()          # 最佳效果圆圈（绿色/红色实线）
    if config.display_mode == "3d_walk":
        stimuli['fixation'].draw()
        emotion_level = show_3d_feedback(win, stimuli['walk_3d_renderer'], feedback_value, baseline_size, success)
    if config.display_mode == "face":
        stimuli['fixation'].draw()
        pupil_shift = show_face_feedback(win, stimuli['face_renderer'], feedback_value, baseline_size, success)

    # 在debug模式下显示反馈信息
    if config.debug_mode:
        status_text = "成功" if success else "失败"

        # 根据显示模式显示不同的信息
        if config.display_mode == "3d_walk":
            stimuli['emotion_level_text'].text = f"情绪等级: {emotion_level} ({status_text})"
        elif config.display_mode == "face":
            stimuli['emotion_level_text'].text = f"瞳孔移动: {pupil_shift:.1f}像素 ({status_text})"
        else:
            stimuli['emotion_level_text'].text = f"圆圈模式 ({status_text})"

        stimuli['pupil_size_text'].text = f"最佳瞳孔: {feedback_value:.3f}mm" if feedback_value is not None else "最佳瞳孔: --"
        stimuli['baseline_pupil_text'].text = f"基线瞳孔: {baseline_size:.3f}mm" if baseline_size is not None else "基线瞳孔: --"

        if feedback_value is not None and baseline_size is not None:
            pupil_diff = feedback_value - baseline_size
            stimuli['pupil_diff_text'].text = f"最佳差值: {pupil_diff:+.3f}mm"
        else:
            stimuli['pupil_diff_text'].text = "最佳差值: --"

        # 绘制debug信息
        stimuli['emotion_level_text'].draw()
        stimuli['pupil_size_text'].draw()
        stimuli['baseline_pupil_text'].draw()
        stimuli['pupil_diff_text'].draw()

    win.flip()

    # 等待反馈时长，在3D模式下持续更新动画
    start_time = core.getTime()
    last_display_time = start_time

    while core.getTime() - start_time < config.feedback_duration:
        current_time = core.getTime()

        # 检查是否还在记录
        error = el_tracker.isRecording()
        if error is not pylink.TRIAL_OK:
            el_tracker.sendMessage('tracker_disconnected')
            abort_trial(win, genv)
            return error

        # 在3D模式和Face模式下，每隔一定时间更新显示（约30Hz）
        if (config.display_mode == "3d_walk" or config.display_mode == "face") and current_time - last_display_time >= 1.0/30:
            last_display_time = current_time

            # 重新绘制动态反馈
            stimuli['fixation'].draw()
            if config.display_mode == "3d_walk":
                emotion_level = show_3d_feedback(win, stimuli['walk_3d_renderer'], feedback_value, baseline_size, success)
            if config.display_mode == "face":
                pupil_shift = show_face_feedback(win, stimuli['face_renderer'], feedback_value, baseline_size, success)

            # 在debug模式下持续显示反馈信息
            if config.debug_mode:
                status_text = "成功" if success else "失败"

                # 根据显示模式显示不同的信息
                if config.display_mode == "3d_walk":
                    stimuli['emotion_level_text'].text = f"情绪等级: {emotion_level} ({status_text})"
                elif config.display_mode == "face":
                    stimuli['emotion_level_text'].text = f"瞳孔移动: {pupil_shift:.1f}像素 ({status_text})"
                else:
                    stimuli['emotion_level_text'].text = f"圆圈模式 ({status_text})"

                stimuli['pupil_size_text'].text = f"最佳瞳孔: {feedback_value:.3f}mm" if feedback_value is not None else "最佳瞳孔: --"
                stimuli['baseline_pupil_text'].text = f"基线瞳孔: {baseline_size:.3f}mm" if baseline_size is not None else "基线瞳孔: --"

                if feedback_value is not None and baseline_size is not None:
                    pupil_diff = feedback_value - baseline_size
                    stimuli['pupil_diff_text'].text = f"最佳差值: {pupil_diff:+.3f}mm"
                else:
                    stimuli['pupil_diff_text'].text = "最佳差值: --"

                # 绘制debug信息
                stimuli['emotion_level_text'].draw()
                stimuli['pupil_size_text'].draw()
                stimuli['baseline_pupil_text'].draw()
                stimuli['pupil_diff_text'].draw()
            
            win.flip()

        # 检查键盘事件
        keyPressList = kb.getKeys(keyList=None, waitRelease=False, clear=False)
        if len(keyPressList) > 0:
            keyPressNamesList = [keyPress.name for keyPress in keyPressList]

            # 按ESC键跳过试次
            if 'escape' in keyPressNamesList:
                el_tracker.sendMessage('trial_skipped_by_user')
                clear_screen(win, genv)
                abort_trial(win, genv)
                return pylink.SKIP_TRIAL

            # 按Ctrl-C终止任务
            if 'c' in keyPressNamesList and ('lctrl' in keyPressNamesList or 'rctrl' in keyPressNamesList):
                el_tracker.sendMessage('terminated_by_user')
                terminate_task(win, genv, None, None, None)
                return pylink.ABORT_EXPT

    el_tracker.sendMessage('PHASE_END FEEDBACK')
    return success

def run_single_trial(win, genv, stimuli, trial_index, condition, should_recal):
    """运行单个试次"""
    # 获取当前活动的EyeLink连接
    el_tracker = pylink.getEYELINK()

    # 设置键盘对象检查按键
    kb = keyboard.Keyboard()

    # 如果需要重新校准
    if should_recal == 'yes':
        recal_msg = '请按ENTER键两次重新校准追踪器'
        show_message(win, genv, recal_msg, wait_for_key=True)
        try:
            el_tracker.doTrackerSetup()
        except RuntimeError as err:
            print('错误:', err)
            el_tracker.exitCalibration()
        should_recal = 'no'

    # 创建瞳孔数据处理器
    pupil_processor = PupilDataProcessor()

    # 设置为离线模式
    el_tracker.setOfflineMode()

    # 清空Host屏幕
    el_tracker.sendCommand('clear_screen 0')

    # 发送试次开始消息
    el_tracker.sendMessage('TRIALID %d' % trial_index)

    # 显示状态信息
    status_msg = '瞳孔生物反馈训练, 试次 %d, 条件: %s' % (trial_index, condition)
    el_tracker.sendCommand("record_status_message '%s'" % status_msg)

    # 再次设置为离线模式
    el_tracker.setOfflineMode()

    # 开始记录
    try:
        el_tracker.startRecording(1, 1, 1, 1)
    except RuntimeError as error:
        print("错误:", error)
        abort_trial(win, genv)
        return pylink.TRIAL_ERROR

    # 等待一些时间让追踪器缓存样本
    pylink.pumpDelay(100)

    # 确定使用哪只眼睛
    eye_used = el_tracker.eyeAvailable()
    if eye_used == 1:
        el_tracker.sendMessage("EYE_USED 1 RIGHT")
    elif eye_used == 0 or eye_used == 2:
        el_tracker.sendMessage("EYE_USED 0 LEFT")
        eye_used = 0
    else:
        print("获取眼睛信息错误!")
        return pylink.TRIAL_ERROR

    try:
        # 1. 基线阶段
        baseline_size = run_baseline_phase(el_tracker, win, genv, stimuli, pupil_processor, kb)
        if isinstance(baseline_size, int):  # 错误代码
            return baseline_size

        # 2. 自主调适阶段
        result, modulation_data = run_modulation_phase(
            el_tracker, win, genv, stimuli, pupil_processor, condition, baseline_size, kb, eye_used)
        if result != pylink.TRIAL_OK:
            return result

        # 3. 反馈阶段
        success = run_feedback_phase(el_tracker, win, genv, stimuli, baseline_size, modulation_data, condition, kb)
        if isinstance(success, int):  # 错误代码
            return success

    except Exception as e:
        print("试次执行错误:", e)
        abort_trial(win, genv)
        return pylink.TRIAL_ERROR

    # 停止记录
    pylink.pumpDelay(100)
    el_tracker.stopRecording()

    # 清空屏幕
    clear_screen(win, genv)
    el_tracker.sendMessage('blank_screen')
    el_tracker.sendMessage('!V CLEAR 128 128 128')

    # 记录试次变量
    el_tracker.sendMessage('!V TRIAL_VAR condition %s' % condition)
    el_tracker.sendMessage('!V TRIAL_VAR baseline_size %.3f' % baseline_size)
    el_tracker.sendMessage('!V TRIAL_VAR success %s' % str(success))

    # 发送试次结果
    el_tracker.sendMessage('TRIAL_RESULT %d' % pylink.TRIAL_OK)

    return should_recal

def run_instruction_phase(win, genv, condition):
    """显示指示语"""
    if condition == "enlarge":
        instruction_text = "下面我们将进行瞳孔的放大训练"
    else:
        instruction_text = "下面我们将进行瞳孔的缩小训练"

    show_message(win, genv, instruction_text, duration=config.instruction_duration)

def run_rest_phase(win, genv):
    """休息阶段"""
    rest_text = "休息时间\n\n请放松眼睛"
    show_message(win, genv, rest_text, duration=config.rest_duration)

def run_experiment_block(win, genv, stimuli, condition, block_index):
    """运行一个实验block"""
    print(f"开始运行 {condition} 条件的第 {block_index+1} 个block")

    # 获取EyeLink连接
    el_tracker = pylink.getEYELINK()

    # 显示指示语
    run_instruction_phase(win, genv, condition)

    # 运行试次
    should_recal = 'no'
    for trial_in_block in range(config.trials_per_block):
        trial_index = block_index * config.trials_per_block + trial_in_block + 1

        # 漂移校正
        if config.drift_check_enabled:
            try:
                error = el_tracker.doDriftCorrect(int(win.size[0]/2.0), int(win.size[1]/2.0), 1, 1)
                if error == pylink.ESC_KEY:
                    el_tracker.doTrackerSetup()
            except:
                pass

        # 运行试次
        result = run_single_trial(win, genv, stimuli, trial_index, condition, should_recal)

        if result == pylink.SKIP_TRIAL:
            continue
        elif result == pylink.ABORT_EXPT:
            return pylink.ABORT_EXPT
        elif result == 'yes':  # 需要重新校准
            should_recal = 'yes'
        else:
            should_recal = 'no'

    # block结束后休息
    run_rest_phase(win, genv)

    return pylink.TRIAL_OK

def main():
    """主函数"""
    # 获取实验信息
    edf_fname, training_order, subject_id = get_experiment_info()

    # 设置结果文件夹
    results_folder = config.results_folder
    if not os.path.exists(results_folder):
        os.makedirs(results_folder)

    # 创建会话标识符
    time_str = time.strftime("_%Y_%m_%d_%H_%M", time.localtime())
    session_identifier = edf_fname + time_str

    # 创建会话文件夹
    session_folder = os.path.join(results_folder, session_identifier)
    if not os.path.exists(session_folder):
        os.makedirs(session_folder)

    try:
        # 设置EyeLink
        el_tracker, edf_file, eyelink_ver = setup_eyelink(edf_fname)

        # 设置PsychoPy窗口
        win, scn_width, scn_height = setup_psychopy_window()

        # 配置EyeLink
        configure_eyelink(el_tracker, eyelink_ver, scn_width, scn_height)

        # 设置图形环境
        genv = setup_graphics_environment(el_tracker, win)

        # 创建视觉刺激
        stimuli = create_visual_stimuli(win)

        # 显示任务说明
        if config.dummy_mode:
            task_msg = '无法在模拟模式下运行此脚本\n按ENTER键退出'
            show_message(win, genv, task_msg, wait_for_key=True)
            terminate_task(win, genv, edf_file, session_folder, session_identifier)
        else:
            task_msg = ('瞳孔生物反馈训练实验\n\n'
                       '实验包含瞳孔放大和缩小训练\n'
                       '请按照屏幕上的指示进行\n'
                       '注视屏幕中央的"+"号\n'
                       '尝试控制瞳孔大小使圆圈变化\n\n'
                       '按Ctrl-C可提前退出实验\n\n'
                       '现在按ENTER键两次进行校准')
            show_message(win, genv, task_msg, wait_for_key=True)

        # 进行校准
        try:
            el_tracker.doTrackerSetup()
        except RuntimeError as err:
            print('错误:', err)
            el_tracker.exitCalibration()

        # 确定训练顺序
        if training_order == "先放大后缩小":
            conditions = ["enlarge", "shrink"]
        else:
            conditions = ["shrink", "enlarge"]

        # 运行实验
        for condition in conditions:
            # 显示条件开始信息
            condition_msg = f"即将开始{('放大' if condition == 'enlarge' else '缩小')}训练\n\n按ENTER键继续"
            show_message(win, genv, condition_msg, wait_for_key=True)

            # 运行3个blocks
            for block_index in range(config.blocks_per_condition):
                result = run_experiment_block(win, genv, stimuli, condition, block_index)

                if result == pylink.ABORT_EXPT:
                    break

            if result == pylink.ABORT_EXPT:
                break

        # 实验结束
        end_msg = "实验结束\n\n感谢您的参与!"
        show_message(win, genv, end_msg, duration=3.0)

    except Exception as e:
        print("实验运行错误:", e)
    finally:
        # 终止任务
        terminate_task(win, genv, edf_file, session_folder, session_identifier)

if __name__ == '__main__':
    main()
