from psychopy import visual, event, core
import numpy as np

# ===== 参数区 =====
win = visual.Window(size=[1000, 700], color='white', units='height')

HEAD_RADIUS = 0.45                     # 头部半径（相对窗口高度）
FACE_DIAM   = HEAD_RADIUS * 2
EYE_FRAC    = 1/3                      # 眼睛水平直径占脸直径的比例
EYE_RX      = FACE_DIAM * EYE_FRAC / 2 # 椭圆水平半径
EYE_RY      = EYE_RX * 0.60            # 垂直半径更小，形成“扁”椭圆

EYE_DX      = 0.22                     # 左右眼中心水平偏移
EYE_DY      = 0.16                     # 眼睛中心垂直偏移（往上）
PUPIL_R     = EYE_RY * 0.55            # 瞳孔半径
pupilShiftX = 0.0                      # 初始水平偏移

MOUTH_Y     = -0.20                    # 嘴巴位置（往下）
MOUTH_HW    = 0.25                     # 嘴半宽

# ===== 函数 =====
def ellipse_vertices(rx, ry, n=128):
    theta = np.linspace(0, 2*np.pi, n, endpoint=False)
    return np.column_stack([rx*np.cos(theta), ry*np.sin(theta)])

# ===== 刺激对象 =====
head = visual.Circle(win, radius=HEAD_RADIUS, lineColor='black', fillColor=None, pos=(0, 0), lineWidth=3)

eyeVerts = ellipse_vertices(EYE_RX, EYE_RY)
eyeL = visual.ShapeStim(win, vertices=eyeVerts, closeShape=True,
                        lineColor='black', fillColor=None, pos=(-EYE_DX, EYE_DY), lineWidth=3)
eyeR = visual.ShapeStim(win, vertices=eyeVerts, closeShape=True,
                        lineColor='black', fillColor=None, pos=( EYE_DX, EYE_DY), lineWidth=3)

pupilL = visual.Circle(win, radius=PUPIL_R, lineColor='black', fillColor='black',
                       pos=(-EYE_DX + pupilShiftX, EYE_DY))
pupilR = visual.Circle(win, radius=PUPIL_R, lineColor='black', fillColor='black',
                       pos=( EYE_DX + pupilShiftX, EYE_DY))

mouth = visual.Line(win, start=(-MOUTH_HW, MOUTH_Y), end=(MOUTH_HW, MOUTH_Y),
                    lineWidth=5, lineColor='black')