#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EyeLink瞳孔直径虚拟圆圈实时显示程序
Real-time Virtual Circle Display for EyeLink Eye Tracker

作者: AI Assistant
日期: 2025-07-02
描述: 从EyeLink眼动仪实时获取瞳孔直径数据并以虚拟圆圈形式显示
"""

import sys
import time
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
from collections import deque
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import matplotlib.font_manager as fm

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

try:
    import pylink
    PYLINK_AVAILABLE = True
except ImportError:
    PYLINK_AVAILABLE = False
    print("警告: PyLink未安装。请在conda eyetracking环境中安装pylink。")
    print("安装命令: pip install --index-url=https://pypi.sr-research.com sr-research-pylink")


class PupilCircleDisplayApp:
    """瞳孔直径虚拟圆圈实时显示应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("EyeLink瞳孔直径虚拟圆圈显示")

        # 设置程序默认全屏
        self.root.state('zoomed')  # Windows下的全屏
        # self.root.attributes('-fullscreen', True)  # 备用全屏方式

        # 配置参数（在显眼位置设置）
        self.scale_factor = tk.DoubleVar(value=1.0)  # 像素到圆圈的比例因子
        self.time_window = tk.IntVar(value=200)  # 时间窗口（毫秒）
        self.max_diameter = tk.DoubleVar(value=2000.0)  # 最大瞳孔直径（像素）
        self.min_diameter = tk.DoubleVar(value=1200.0)  # 最小瞳孔直径（像素）
        self.auto_start = tk.BooleanVar(value=True)  # 自动连接并开始记录
        
        # 数据存储
        self.max_samples = 1000
        self.pupil_data_with_time = deque(maxlen=self.max_samples)  # 存储(时间戳, 平均瞳孔值)
        self.current_average_pupil = 0.0
        
        # EyeLink相关
        self.tracker = None
        self.is_connected = False
        self.is_recording = False
        self.data_thread = None
        self.stop_thread = False
        
        # 创建GUI
        self.create_widgets()
        
        # 初始化圆圈显示
        self.setup_circle_display()

        # 如果启用自动启动，延迟执行自动连接
        if self.auto_start.get():
            self.root.after(1000, self.auto_connect_and_record)  # 1秒后自动连接
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置参数框架（最显眼的位置）
        config_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 第一行：自动启动设置（最显眼）
        ttk.Label(config_frame, text="自动连接并记录:", font=("Arial", 12, "bold"), foreground="red").grid(row=0, column=0, sticky=tk.W)
        auto_check = ttk.Checkbutton(config_frame, text="启用", variable=self.auto_start,
                                    command=self.on_auto_start_change)
        auto_check.grid(row=0, column=1, sticky=tk.W, padx=(5, 30))

        # 手动控制按钮
        self.manual_connect_btn = ttk.Button(config_frame, text="手动连接", command=self.manual_connect)
        self.manual_connect_btn.grid(row=0, column=2, padx=(0, 10))

        self.manual_record_btn = ttk.Button(config_frame, text="手动记录", command=self.manual_record, state="disabled")
        self.manual_record_btn.grid(row=0, column=3, padx=(0, 20))

        # 第二行：其他参数设置
        ttk.Label(config_frame, text="圆圈比例因子:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        scale_spinbox = ttk.Spinbox(config_frame, from_=0.1, to=10.0, increment=0.1,
                                   textvariable=self.scale_factor, width=10, font=("Arial", 10))
        scale_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(5, 20), pady=(10, 0))

        # 时间窗口设置
        ttk.Label(config_frame, text="时间窗口(ms):", font=("Arial", 10, "bold")).grid(row=1, column=2, sticky=tk.W, pady=(10, 0))
        time_spinbox = ttk.Spinbox(config_frame, from_=50, to=1000, increment=50,
                                  textvariable=self.time_window, width=10, font=("Arial", 10))
        time_spinbox.grid(row=1, column=3, sticky=tk.W, padx=(5, 20), pady=(10, 0))

        # 第三行：最大和最小直径设置
        ttk.Label(config_frame, text="最大直径(像素):", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        max_spinbox = ttk.Spinbox(config_frame, from_=100.0, to=5000.0, increment=50.0,
                                 textvariable=self.max_diameter, width=10, font=("Arial", 10))
        max_spinbox.grid(row=2, column=1, sticky=tk.W, padx=(5, 20), pady=(10, 0))

        ttk.Label(config_frame, text="最小直径(像素):", font=("Arial", 10, "bold")).grid(row=2, column=2, sticky=tk.W, pady=(10, 0))
        min_spinbox = ttk.Spinbox(config_frame, from_=50.0, to=3000.0, increment=50.0,
                                 textvariable=self.min_diameter, width=10, font=("Arial", 10))
        min_spinbox.grid(row=2, column=3, sticky=tk.W, padx=(5, 20), pady=(10, 0))
        
        # EyeLink控制框架
        control_frame = ttk.LabelFrame(main_frame, text="EyeLink控制", padding="5")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="连接EyeLink", 
                                     command=self.connect_tracker)
        self.connect_btn.grid(row=0, column=0, padx=(0, 5))
        
        # 开始/停止记录按钮
        self.record_btn = ttk.Button(control_frame, text="开始记录", 
                                    command=self.toggle_recording, state="disabled")
        self.record_btn.grid(row=0, column=1, padx=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(control_frame, text="状态: 未连接")
        self.status_label.grid(row=0, column=2, padx=(10, 0))
        
        # 数据显示框架
        data_frame = ttk.LabelFrame(main_frame, text="实时数据", padding="5")
        data_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 当前平均瞳孔直径
        ttk.Label(data_frame, text="当前平均瞳孔:").grid(row=0, column=0, sticky=tk.W)
        self.current_pupil_label = ttk.Label(data_frame, text="-- 像素", font=("Arial", 12, "bold"), foreground="blue")
        self.current_pupil_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 最大瞳孔直径显示
        ttk.Label(data_frame, text="最大瞳孔直径:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.max_pupil_label = ttk.Label(data_frame, text="2000.0 像素", font=("Arial", 12, "bold"), foreground="green")
        self.max_pupil_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))

        # 最小瞳孔直径显示
        ttk.Label(data_frame, text="最小瞳孔直径:").grid(row=0, column=4, sticky=tk.W, padx=(20, 0))
        self.min_pupil_label = ttk.Label(data_frame, text="1200.0 像素", font=("Arial", 12, "bold"), foreground="red")
        self.min_pupil_label.grid(row=0, column=5, sticky=tk.W, padx=(10, 0))
        
        # 圆圈显示框架
        self.circle_frame = ttk.LabelFrame(main_frame, text="瞳孔圆圈显示", padding="5")
        self.circle_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
    def setup_circle_display(self):
        """设置圆圈显示"""
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.ax.set_xlim(-100, 100)
        self.ax.set_ylim(-100, 100)
        self.ax.set_aspect('equal')
        self.ax.set_title("瞳孔直径虚拟圆圈显示", fontsize=16, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel("X 坐标", fontsize=12)
        self.ax.set_ylabel("Y 坐标", fontsize=12)
        
        # 创建最大直径圆圈（虚线，绿色）
        self.max_circle = Circle((0, 0), 50, fill=False, linestyle='--',
                                linewidth=4, color='green', alpha=0.8, label='最大直径')
        self.ax.add_patch(self.max_circle)

        # 创建最小直径圆圈（虚线，红色）
        self.min_circle = Circle((0, 0), 30, fill=False, linestyle='--',
                                linewidth=4, color='red', alpha=0.8, label='最小直径')
        self.ax.add_patch(self.min_circle)

        # 创建实时圆圈（实线，蓝色）
        self.current_circle = Circle((0, 0), 20, fill=False, linestyle='-',
                                    linewidth=4, color='blue', alpha=0.9, label='当前直径')
        self.ax.add_patch(self.current_circle)

        # 添加中心点
        self.center_point = self.ax.plot(0, 0, 'ko', markersize=10, label='中心点')[0]

        # 添加刻度标记（类似论文中的小方块）
        self.scale_markers = []
        for i in range(8):
            angle = i * np.pi / 4  # 每45度一个标记
            marker = self.ax.plot([], [], 's', markersize=8, color='green', alpha=0.6)[0]
            self.scale_markers.append(marker)

        # 添加背景圆圈作为参考
        for radius in [10, 20, 30, 40, 50, 60]:
            bg_circle = Circle((0, 0), radius, fill=False, linestyle=':',
                             linewidth=1, color='gray', alpha=0.3)
            self.ax.add_patch(bg_circle)

        # 添加图例
        self.ax.legend(loc='upper right', fontsize=10)
        
        # 嵌入到tkinter中
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        self.canvas = FigureCanvasTkAgg(self.fig, self.circle_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 设置动画
        self.ani = animation.FuncAnimation(self.fig, self.update_circles,
                                          interval=50, blit=False, cache_frame_data=False)

    def connect_tracker(self):
        """连接到EyeLink眼动仪"""
        if not PYLINK_AVAILABLE:
            messagebox.showerror("错误", "PyLink未安装。请先安装pylink库。")
            return

        try:
            # 初始化EyeLink连接
            self.tracker = pylink.EyeLink("*********")  # 默认EyeLink IP地址

            # 获取眼动仪信息
            eyelink_ver = self.tracker.getTrackerVersion()
            print(f"连接到EyeLink {eyelink_ver}")

            # 配置眼动仪
            self.configure_tracker()

            self.is_connected = True
            self.status_label.config(text="状态: 已连接")
            self.connect_btn.config(text="断开连接", command=self.disconnect_tracker)
            self.record_btn.config(state="normal")
            self.manual_connect_btn.config(text="断开连接", command=self.disconnect_tracker)
            self.manual_record_btn.config(state="normal")

            if not self.auto_start.get():  # 只有非自动模式才显示消息框
                messagebox.showinfo("成功", "成功连接到EyeLink眼动仪！")

        except Exception as e:
            messagebox.showerror("连接错误", f"无法连接到EyeLink眼动仪:\n{str(e)}")
            print(f"连接错误: {e}")

    def configure_tracker(self):
        """配置EyeLink眼动仪设置"""
        if not self.tracker:
            return

        # 设置采样率
        self.tracker.sendCommand("sample_rate 1000")

        # 设置记录的数据类型
        self.tracker.sendCommand("file_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT")
        self.tracker.sendCommand("file_sample_data = LEFT,RIGHT,GAZE,HREF,AREA,GAZERES,STATUS,INPUT")
        self.tracker.sendCommand("link_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE,INPUT")
        self.tracker.sendCommand("link_sample_data = LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS,INPUT")

    def disconnect_tracker(self):
        """断开EyeLink连接"""
        if self.is_recording:
            self.stop_recording()

        if self.tracker:
            self.tracker.close()
            self.tracker = None

        self.is_connected = False
        self.status_label.config(text="状态: 未连接")
        self.connect_btn.config(text="连接EyeLink", command=self.connect_tracker)
        self.record_btn.config(state="disabled")
        self.manual_connect_btn.config(text="手动连接", command=self.manual_connect)
        self.manual_record_btn.config(state="disabled")

    def toggle_recording(self):
        """切换记录状态"""
        if self.is_recording:
            self.stop_recording()
        else:
            self.start_recording()

    def start_recording(self):
        """开始记录数据"""
        if not self.is_connected or not self.tracker:
            return

        try:
            # 开始记录
            self.tracker.startRecording(1, 1, 1, 1)
            import pylink
            pylink.pumpDelay(100)  # 等待记录开始

            self.is_recording = True
            self.stop_thread = False
            self.record_btn.config(text="停止记录")
            self.status_label.config(text="状态: 正在记录")

            # 启动数据采集线程
            self.data_thread = threading.Thread(target=self.data_collection_loop)
            self.data_thread.daemon = True
            self.data_thread.start()

        except Exception as e:
            messagebox.showerror("记录错误", f"无法开始记录:\n{str(e)}")

    def stop_recording(self):
        """停止记录数据"""
        if not self.is_recording:
            return

        self.stop_thread = True
        self.is_recording = False

        if self.tracker:
            self.tracker.stopRecording()

        self.record_btn.config(text="开始记录")
        self.status_label.config(text="状态: 已连接")

    def data_collection_loop(self):
        """数据采集循环（在单独线程中运行）"""
        start_time = time.time()

        while not self.stop_thread and self.is_recording:
            try:
                # 获取最新样本
                dt = self.tracker.getNewestSample()

                if dt is not None:
                    current_time = time.time()

                    # 获取左眼和右眼数据
                    left_pupil = None
                    right_pupil = None

                    if dt.isLeftSample():
                        left_pupil = dt.getLeftEye().getPupilSize()

                    if dt.isRightSample():
                        right_pupil = dt.getRightEye().getPupilSize()

                    # 计算平均瞳孔值
                    average_pupil = self.calculate_average_pupil(left_pupil, right_pupil)

                    if not np.isnan(average_pupil):
                        # 存储数据（时间戳，平均瞳孔值）
                        self.pupil_data_with_time.append((current_time, average_pupil))

                        # 计算时间窗口内的平均值
                        window_average = self.calculate_window_average(current_time)

                        # 更新当前平均值
                        self.current_average_pupil = window_average

                        # 更新GUI显示
                        self.root.after(0, self.update_display)

                time.sleep(0.001)  # 1ms延迟

            except Exception as e:
                print(f"数据采集错误: {e}")
                break

    def calculate_average_pupil(self, left_pupil, right_pupil):
        """计算两眼的平均瞳孔值"""
        valid_left = left_pupil if left_pupil and left_pupil > 0 else np.nan
        valid_right = right_pupil if right_pupil and right_pupil > 0 else np.nan

        if not np.isnan(valid_left) and not np.isnan(valid_right):
            return (valid_left + valid_right) / 2
        elif not np.isnan(valid_left):
            return valid_left
        elif not np.isnan(valid_right):
            return valid_right
        else:
            return np.nan

    def calculate_window_average(self, current_time):
        """计算指定时间窗口内的平均瞳孔值"""
        window_ms = self.time_window.get() / 1000.0  # 转换为秒
        cutoff_time = current_time - window_ms

        # 过滤时间窗口内的数据
        window_data = [pupil for timestamp, pupil in self.pupil_data_with_time
                      if timestamp >= cutoff_time]

        if window_data:
            return np.mean(window_data)
        else:
            return self.current_average_pupil  # 如果没有数据，保持当前值

    def update_display(self):
        """更新GUI显示"""
        # 更新当前瞳孔值显示
        self.current_pupil_label.config(text=f"{self.current_average_pupil:.1f} 像素")

        # 更新最大和最小瞳孔值显示
        max_value = self.max_diameter.get()
        min_value = self.min_diameter.get()
        self.max_pupil_label.config(text=f"{max_value:.1f} 像素")
        self.min_pupil_label.config(text=f"{min_value:.1f} 像素")

    def update_circles(self, frame):
        """更新圆圈显示"""
        # 获取当前参数
        scale = self.scale_factor.get()
        max_diameter = self.max_diameter.get()
        min_diameter = self.min_diameter.get()

        # 计算圆圈半径
        max_radius = (max_diameter * scale) / 2
        min_radius = (min_diameter * scale) / 2
        current_radius = (self.current_average_pupil * scale) / 2

        # 更新最大直径圆圈（虚线，绿色）
        self.max_circle.set_radius(max_radius)

        # 更新最小直径圆圈（虚线，红色）
        self.min_circle.set_radius(min_radius)

        # 更新当前圆圈（实线，蓝色）
        self.current_circle.set_radius(current_radius)

        # 更新刻度标记位置（在最大圆圈上）
        for i, marker in enumerate(self.scale_markers):
            angle = i * np.pi / 4  # 每45度一个标记
            x = max_radius * np.cos(angle)
            y = max_radius * np.sin(angle)
            marker.set_data([x], [y])

        # 动态调整坐标轴范围
        display_max_radius = max(max_radius, min_radius, current_radius, 30)  # 最小显示范围30
        axis_limit = display_max_radius * 1.2
        self.ax.set_xlim(-axis_limit, axis_limit)
        self.ax.set_ylim(-axis_limit, axis_limit)

        # 返回需要更新的艺术家对象
        return [self.max_circle, self.min_circle, self.current_circle] + self.scale_markers

    def on_auto_start_change(self):
        """自动启动设置改变时的回调"""
        if self.auto_start.get():
            print("自动启动已启用")
            # 如果当前未连接，立即尝试连接
            if not self.is_connected:
                self.root.after(500, self.auto_connect_and_record)
        else:
            print("自动启动已禁用")

    def auto_connect_and_record(self):
        """自动连接并开始记录"""
        if not PYLINK_AVAILABLE:
            print("PyLink未安装，无法自动连接")
            return

        try:
            print("正在自动连接EyeLink...")
            self.connect_tracker()

            # 连接成功后自动开始记录
            if self.is_connected:
                self.root.after(1000, self.auto_start_recording)  # 连接后1秒开始记录

        except Exception as e:
            print(f"自动连接失败: {e}")

    def auto_start_recording(self):
        """自动开始记录"""
        if self.is_connected and not self.is_recording:
            print("自动开始记录...")
            self.start_recording()

    def manual_connect(self):
        """手动连接"""
        self.connect_tracker()

    def manual_record(self):
        """手动记录"""
        self.toggle_recording()

    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.is_recording:
            self.stop_recording()
        if self.is_connected:
            self.disconnect_tracker()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """主函数"""
    print("EyeLink瞳孔直径虚拟圆圈显示程序")
    print("=" * 50)
    print("功能特点:")
    print("1. 虚拟圆圈显示瞳孔直径")
    print("2. 可调节的比例因子")
    print("3. 可设置的时间窗口平均值")
    print("4. 目标直径设置")
    print("5. 实时圆圈大小变化")

    if not PYLINK_AVAILABLE:
        print("\n警告: PyLink未安装")
        print("请在conda eyetracking环境中运行以下命令安装:")
        print("pip install --index-url=https://pypi.sr-research.com sr-research-pylink")
        print("\n程序将以演示模式运行...")

    app = PupilCircleDisplayApp()
    app.run()


if __name__ == "__main__":
    main()
