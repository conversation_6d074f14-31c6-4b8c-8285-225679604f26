# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练各阶段调试测试程序
用于测试基线、自主调节、反馈三个阶段的显示问题

作者: AI Assistant
日期: 2025-07-23
"""

import os
import sys
import numpy as np
import time
from psychopy import visual, core, event, monitors
from psychopy.hardware import keyboard

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

# 导入配置和模块
from config import config
if config.display_mode == "3d_walk":
    import walk_3d
    from walk_3d import Walk3DRenderer, show_3d_baseline, show_3d_modulation, show_3d_feedback

class MockPupilProcessor:
    """模拟瞳孔数据处理器"""
    def __init__(self):
        self.baseline_pupil_size = 3.5  # 基线瞳孔大小 (mm)
        self.current_pupil_size = 3.5   # 当前瞳孔大小 (mm)
        
    def set_baseline(self):
        """设置基线"""
        return self.baseline_pupil_size
        
    def get_averaged_pupil_size(self):
        """获取平均瞳孔大小"""
        return self.current_pupil_size
        
    def simulate_pupil_change(self, target_size, duration, steps=100):
        """模拟瞳孔变化"""
        start_size = self.current_pupil_size
        for i in range(steps):
            progress = i / (steps - 1)
            self.current_pupil_size = start_size + (target_size - start_size) * progress
            yield self.current_pupil_size
            time.sleep(duration / steps)

def setup_test_window():
    """设置测试窗口"""
    mon = monitors.Monitor('myMonitor', width=config.monitor_width, distance=config.monitor_distance)
    win = visual.Window(
        # size=[1920, 1080],
        size=[800, 600],
        monitor=mon,
        winType='pyglet',
        units='pix',
        color=config.bg_color,
        fullscr=False  # 测试时不全屏
    )
    return win

def create_test_stimuli(win):
    """创建测试刺激"""
    stimuli = {}
    
    # 注视点
    stimuli['fixation'] = visual.TextStim(win, '+',
                                         height=50,
                                         color=config.fixation_color,
                                         pos=(0, 0))
    
    # 状态文本
    stimuli['status_text'] = visual.TextStim(
        win,
        text="",
        pos=(0, 300),
        height=20,
        color=[1, 1, 1],
        font=config.font_name
    )
    
    # 详细信息文本
    stimuli['info_text'] = visual.TextStim(
        win,
        text="",
        pos=(0, -300),
        height=16,
        color=[1, 1, 1],
        font=config.font_name,
        wrapWidth=800
    )
    
    if config.display_mode == "3d_walk":
        # 创建3D小人渲染器
        stimuli['walk_3d_renderer'] = Walk3DRenderer(win)
        print("3D小人渲染器已创建")
    
    return stimuli

def test_baseline_phase(win, stimuli, duration=5.0):
    """测试基线阶段"""
    print("\n=== 测试基线阶段 ===")
    print(f"测试时长: {duration}秒")
    print("基线阶段应该显示情绪0的动态小人动画")
    
    # 创建模拟处理器
    pupil_processor = MockPupilProcessor()
    baseline_size = pupil_processor.set_baseline()
    
    start_time = core.getTime()
    frame_count = 0
    
    while core.getTime() - start_time < duration:
        current_time = core.getTime()
        elapsed_time = current_time - start_time
        
        # 清空屏幕
        win.clearBuffer()
        
        # 更新状态文本
        stimuli['status_text'].text = f"基线阶段测试 - 时间: {elapsed_time:.1f}s / {duration}s"
        
        # 更新信息文本
        info_text = (f"基线瞳孔大小: {baseline_size:.3f}mm\n"
                    f"帧数: {frame_count}\n"
                    f"应该显示: 情绪0的动态动画")
        stimuli['info_text'].text = info_text
        
        # 显示注视点
        stimuli['fixation'].draw()
        
        if config.display_mode == "3d_walk":
            # 显示3D小人基线动画
            show_3d_baseline(win, stimuli['walk_3d_renderer'], baseline_size)
        
        # 显示文本
        stimuli['status_text'].draw()
        stimuli['info_text'].draw()
        
        # 刷新屏幕
        win.flip()
        frame_count += 1
        
        # 检查退出键
        keys = event.getKeys()
        if 'escape' in keys:
            break
    
    print(f"基线阶段测试完成，共显示 {frame_count} 帧")
    return baseline_size

def test_modulation_phase(win, stimuli, baseline_size, duration=10.0):
    """测试自主调节阶段"""
    print("\n=== 测试自主调节阶段 ===")
    print(f"测试时长: {duration}秒")
    print("自主调节阶段应该根据瞳孔变化显示不同情绪等级的动画")
    
    # 创建模拟处理器
    pupil_processor = MockPupilProcessor()
    pupil_processor.baseline_pupil_size = baseline_size
    
    start_time = core.getTime()
    frame_count = 0
    modulation_data = []
    
    # 模拟瞳孔变化：从基线开始，先放大再缩小
    pupil_changes = [
        (baseline_size, 2.0),      # 保持基线2秒
        (baseline_size + 1.0, 2.0), # 放大1mm，持续2秒
        (baseline_size + 1.5, 2.0), # 放大1.5mm，持续2秒
        (baseline_size - 0.5, 2.0), # 缩小0.5mm，持续2秒
        (baseline_size, 2.0),      # 回到基线2秒
    ]
    
    change_index = 0
    change_start_time = start_time
    target_pupil = pupil_changes[0][0]
    
    while core.getTime() - start_time < duration:
        current_time = core.getTime()
        elapsed_time = current_time - start_time
        
        # 更新瞳孔大小模拟
        if change_index < len(pupil_changes):
            change_elapsed = current_time - change_start_time
            change_duration = pupil_changes[change_index][1]
            
            if change_elapsed >= change_duration:
                # 切换到下一个变化
                change_index += 1
                if change_index < len(pupil_changes):
                    change_start_time = current_time
                    target_pupil = pupil_changes[change_index][0]
            
            # 平滑过渡到目标瞳孔大小
            current_pupil = pupil_processor.get_averaged_pupil_size()
            diff = target_pupil - current_pupil
            pupil_processor.current_pupil_size += diff * 0.1  # 平滑过渡
        
        current_pupil_size = pupil_processor.get_averaged_pupil_size()
        modulation_data.append(current_pupil_size)
        
        # 清空屏幕
        win.clearBuffer()
        
        # 计算情绪等级
        if config.display_mode == "3d_walk":
            emotion_level = stimuli['walk_3d_renderer'].pupil_to_emotion_level(
                current_pupil_size, baseline_size)
        else:
            emotion_level = 0
        
        # 更新状态文本
        stimuli['status_text'].text = f"自主调节阶段测试 - 时间: {elapsed_time:.1f}s / {duration}s"
        
        # 更新信息文本
        info_text = (f"基线瞳孔: {baseline_size:.3f}mm\n"
                    f"当前瞳孔: {current_pupil_size:.3f}mm\n"
                    f"瞳孔差值: {current_pupil_size - baseline_size:.3f}mm\n"
                    f"情绪等级: {emotion_level}\n"
                    f"帧数: {frame_count}")
        stimuli['info_text'].text = info_text
        
        # 显示注视点
        stimuli['fixation'].draw()
        
        if config.display_mode == "3d_walk":
            # 显示3D小人调节动画
            show_3d_modulation(win, stimuli['walk_3d_renderer'], current_pupil_size, baseline_size)
        
        # 显示文本
        stimuli['status_text'].draw()
        stimuli['info_text'].draw()
        
        # 刷新屏幕
        win.flip()
        frame_count += 1
        
        # 检查退出键
        keys = event.getKeys()
        if 'escape' in keys:
            break
        
        # 控制帧率
        core.wait(1.0 / 60)
    
    print(f"自主调节阶段测试完成，共显示 {frame_count} 帧")
    print(f"瞳孔数据范围: {min(modulation_data):.3f} - {max(modulation_data):.3f}mm")
    return modulation_data

def test_feedback_phase(win, stimuli, baseline_size, modulation_data, duration=3.0):
    """测试反馈阶段"""
    print("\n=== 测试反馈阶段 ===")
    print(f"测试时长: {duration}秒")
    print("反馈阶段应该显示最佳效果的静态小人，颜色表示成功/失败")
    
    # 分析调节数据
    if len(modulation_data) > 0:
        max_value = max(modulation_data)
        min_value = min(modulation_data)
        
        # 假设是放大条件
        if max_value > baseline_size:
            success = True
            feedback_value = max_value
            condition = "enlarge"
        else:
            success = False
            feedback_value = max_value
            condition = "enlarge"
    else:
        success = False
        feedback_value = baseline_size
        condition = "enlarge"
    
    start_time = core.getTime()
    frame_count = 0
    
    while core.getTime() - start_time < duration:
        current_time = core.getTime()
        elapsed_time = current_time - start_time
        
        # 清空屏幕
        win.clearBuffer()
        
        # 计算反馈情绪等级
        if config.display_mode == "3d_walk":
            emotion_level = stimuli['walk_3d_renderer'].pupil_to_emotion_level(
                feedback_value, baseline_size)
        else:
            emotion_level = 0
        
        # 更新状态文本
        stimuli['status_text'].text = f"反馈阶段测试 - 时间: {elapsed_time:.1f}s / {duration}s"
        
        # 更新信息文本
        info_text = (f"基线瞳孔: {baseline_size:.3f}mm\n"
                    f"最佳效果: {feedback_value:.3f}mm\n"
                    f"训练条件: {condition}\n"
                    f"训练结果: {'成功' if success else '失败'}\n"
                    f"情绪等级: {emotion_level}\n"
                    f"帧数: {frame_count}")
        stimuli['info_text'].text = info_text
        
        # 显示注视点
        stimuli['fixation'].draw()
        
        if config.display_mode == "3d_walk":
            # 显示3D小人反馈
            show_3d_feedback(win, stimuli['walk_3d_renderer'], feedback_value, baseline_size, success)
        
        # 显示文本
        stimuli['status_text'].draw()
        stimuli['info_text'].draw()
        
        # 刷新屏幕
        win.flip()
        frame_count += 1
        
        # 检查退出键
        keys = event.getKeys()
        if 'escape' in keys:
            break
    
    print(f"反馈阶段测试完成，共显示 {frame_count} 帧")
    return success

def main():
    """主测试函数"""
    print("瞳孔生物反馈训练各阶段调试测试程序")
    print("=" * 50)
    print(f"显示模式: {config.display_mode}")
    print("按ESC键可以跳过当前阶段")
    print("=" * 50)
    
    # 设置窗口
    win = setup_test_window()
    
    try:
        # 创建刺激
        stimuli = create_test_stimuli(win)
        
        # 显示开始信息
        start_text = visual.TextStim(
            win,
            text="各阶段调试测试即将开始\n\n按任意键继续...",
            pos=(0, 0),
            height=30,
            color=[1, 1, 1],
            font=config.font_name
        )
        start_text.draw()
        win.flip()
        event.waitKeys()
        
        # 1. 测试基线阶段
        baseline_size = test_baseline_phase(win, stimuli, duration=5.0)
        
        # 2. 测试自主调节阶段
        modulation_data = test_modulation_phase(win, stimuli, baseline_size, duration=10.0)
        
        # 3. 测试反馈阶段
        success = test_feedback_phase(win, stimuli, baseline_size, modulation_data, duration=3.0)
        
        # 显示测试总结
        summary_text = (f"测试完成!\n\n"
                       f"基线瞳孔大小: {baseline_size:.3f}mm\n"
                       f"调节数据点数: {len(modulation_data)}\n"
                       f"最终训练结果: {'成功' if success else '失败'}\n\n"
                       f"按任意键退出...")
        
        summary_display = visual.TextStim(
            win,
            text=summary_text,
            pos=(0, 0),
            height=25,
            color=[1, 1, 1],
            font=config.font_name
        )
        summary_display.draw()
        win.flip()
        event.waitKeys()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭窗口
        win.close()
        core.quit()

if __name__ == '__main__':
    main()
