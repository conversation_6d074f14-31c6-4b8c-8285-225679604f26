# -*- coding: utf-8 -*-
"""
测试反馈阶段动态动画效果
验证3D小人在反馈阶段显示动态的最佳调节效果情绪等级动图

作者: AI Assistant
日期: 2025-07-23
"""

import os
import sys
import time
from psychopy import visual, core, event, monitors
from config import config
from walk_3d import Walk3<PERSON>enderer, show_3d_feedback

def test_feedback_animation():
    """测试反馈阶段的动态动画效果"""
    print("=" * 60)
    print("测试反馈阶段动态动画效果")
    print("=" * 60)
    
    # 设置窗口
    mon = monitors.Monitor('myMonitor', width=config.monitor_width, distance=config.monitor_distance)
    win = visual.Window(
        size=[1920, 1080],
        monitor=mon,
        winType='pyglet',
        units='pix',
        color=config.bg_color,
        fullscr=False  # 测试时使用窗口模式
    )
    
    # 创建3D渲染器
    renderer = Walk3DRenderer(win)
    
    # 创建注视点
    fixation = visual.TextStim(win, '+',
                              height=50,
                              color=config.fixation_color,
                              pos=(0, 0))
    
    # 创建状态显示文本
    status_text = visual.TextStim(
        win,
        text="",
        pos=(0, 300),
        height=20,
        color=[1, 1, 1],
        font=config.font_name
    )
    
    # 创建说明文本
    instruction_text = visual.TextStim(
        win,
        text="反馈阶段动画测试\n\n按键控制:\n1-6: 测试不同的最佳效果瞳孔大小\nS: 切换成功/失败状态\nESC: 退出",
        pos=(0, -300),
        height=16,
        color=[1, 1, 1],
        font=config.font_name,
        wrapWidth=600
    )
    
    # 测试参数
    baseline_pupil_mm = 4.0  # 基线瞳孔大小
    test_cases = [
        {"best_effect": 3.0, "condition": "shrink", "description": "缩小训练 - 最小值3.0mm"},
        {"best_effect": 3.5, "condition": "shrink", "description": "缩小训练 - 最小值3.5mm"},
        {"best_effect": 4.5, "condition": "enlarge", "description": "放大训练 - 最大值4.5mm"},
        {"best_effect": 5.0, "condition": "enlarge", "description": "放大训练 - 最大值5.0mm"},
        {"best_effect": 5.5, "condition": "enlarge", "description": "放大训练 - 最大值5.5mm"},
        {"best_effect": 6.0, "condition": "enlarge", "description": "放大训练 - 最大值6.0mm"},
    ]
    
    current_test_index = 0
    success = True  # 默认成功状态
    
    print("开始反馈动画测试")
    print(f"基线瞳孔大小: {baseline_pupil_mm}mm")
    print("使用数字键1-6切换不同测试案例，S键切换成功/失败状态")
    
    # 主循环
    while True:
        current_time = core.getTime()
        current_test = test_cases[current_test_index]
        
        # 检查键盘输入
        keys = event.getKeys()
        for key in keys:
            if key == 'escape':
                win.close()
                core.quit()
                return
            elif key in ['1', '2', '3', '4', '5', '6']:
                test_index = int(key) - 1
                if 0 <= test_index < len(test_cases):
                    current_test_index = test_index
                    # 重置动画时间
                    if hasattr(renderer, 'feedback_animation_start_time'):
                        delattr(renderer, 'feedback_animation_start_time')
                    print(f"切换到测试案例 {test_index + 1}: {test_cases[test_index]['description']}")
            elif key == 's':
                success = not success
                # 重置动画时间
                if hasattr(renderer, 'feedback_animation_start_time'):
                    delattr(renderer, 'feedback_animation_start_time')
                print(f"切换成功状态: {'成功' if success else '失败'}")
        
        # 计算情绪等级
        best_effect_pupil_mm = current_test['best_effect']
        emotion_level = renderer.pupil_to_emotion_level(best_effect_pupil_mm, baseline_pupil_mm)
        
        # 清空屏幕
        win.clearBuffer()
        
        # 更新状态文本
        status_text.text = (f"测试案例 {current_test_index + 1}: {current_test['description']}\n"
                           f"基线: {baseline_pupil_mm}mm | 最佳效果: {best_effect_pupil_mm}mm | "
                           f"情绪等级: {emotion_level} | 状态: {'成功' if success else '失败'}")
        
        # 绘制注视点
        fixation.draw()
        
        # 显示3D小人反馈动画
        show_3d_feedback(win, renderer, best_effect_pupil_mm, baseline_pupil_mm, success)
        
        # 绘制文本
        status_text.draw()
        instruction_text.draw()
        
        # 刷新屏幕
        win.flip()
        
        # 控制帧率
        core.wait(1.0 / 60)  # 60 FPS

def test_emotion_mapping():
    """测试瞳孔大小到情绪等级的映射"""
    print("\n" + "=" * 60)
    print("测试瞳孔大小到情绪等级的映射")
    print("=" * 60)
    
    # 创建临时渲染器用于测试映射函数
    from psychopy import visual, monitors
    mon = monitors.Monitor('myMonitor')
    win = visual.Window(size=[100, 100], monitor=mon, winType='pyglet', units='pix')
    renderer = Walk3DRenderer(win)
    
    baseline_pupil_mm = 4.0
    test_pupil_sizes = [2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0]
    
    print(f"基线瞳孔大小: {baseline_pupil_mm}mm")
    print(f"瞳孔映射系数: {config.pupil_emotion_scale}")
    print(f"情绪步长: {config.emotion_step_size}")
    print("-" * 60)
    print("瞳孔大小(mm) | 瞳孔差值 | 映射值 | 情绪等级 | 训练方向")
    print("-" * 60)
    
    for pupil_size in test_pupil_sizes:
        emotion_level = renderer.pupil_to_emotion_level(pupil_size, baseline_pupil_mm)
        pupil_diff = pupil_size - baseline_pupil_mm
        mapped_value = config.pupil_emotion_scale * pupil_diff
        
        if pupil_size > baseline_pupil_mm:
            direction = "放大训练适用"
        elif pupil_size < baseline_pupil_mm:
            direction = "缩小训练适用"
        else:
            direction = "基线"
        
        print(f"{pupil_size:8.1f}     | {pupil_diff:7.1f}  | {mapped_value:6.1f} | {emotion_level:8d} | {direction}")
    
    win.close()

if __name__ == '__main__':
    print("反馈阶段动态动画测试程序")
    
    # 测试情绪映射
    test_emotion_mapping()
    
    # 测试反馈动画
    test_feedback_animation()
